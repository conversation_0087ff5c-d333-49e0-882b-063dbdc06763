{"name": "webpack-vue-project", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^0.24.0", "buffer": "^6.0.3", "core-js": "^3.6.5", "dayjs": "^1.10.7", "echarts": "^5.6.0", "file-saver": "^2.0.5", "leaflet": "^1.9.4", "leaflet-boundary-canvas": "^1.0.0", "moment": "^2.29.1", "process": "^0.11.10", "proj4": "^2.17.0", "proj4leaflet": "^1.0.2", "vue": "^3.5.14", "vue-loader": "^16.8.3", "vue-router": "^4.0.0", "vuex": "^4.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.5.14", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "less": "^3.0.4", "less-loader": "^5.0.0"}, "babel": {"presets": ["@babel/preset-env"], "plugins": ["@babel/plugin-transform-optional-chaining"]}}