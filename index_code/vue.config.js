const path = require('path');
const webpack = require('webpack');
const { createProxyMiddleware } = require('http-proxy-middleware');


module.exports = {
  lintOnSave: false,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      }

    },
    plugins: [
      // 解决 xlsx 依赖问题
      new webpack.ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer'],
      }),
    ],
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              plugins: ['@babel/plugin-transform-optional-chaining']
            }
          }
        }
      ]
    }
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        }
      }
    }
  },
  devServer: {
    proxy: {
      '/photovoltaic-manager': {
        target: 'http://localhost:8090/photovoltaic-manager',
        pathRewrite: {
          '^/photovoltaic-manager': ''
        }
      }
    },
    logLevel: 'debug' // 添加调试日志
  }
  // 移除 chainWebpack 配置
}
