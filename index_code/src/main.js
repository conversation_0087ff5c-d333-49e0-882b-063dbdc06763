import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/index'

// 使用相对路径导入样式
import './assets/styles/common.less';

// 导入 Leaflet CSS
import 'leaflet/dist/leaflet.css';

// antd
import 'ant-design-vue/dist/reset.css';
import Antd, { message } from 'ant-design-vue'

message.config({
    top: '70px',
})

import 'dayjs/locale/zh-cn';

import moment from 'moment';
import 'moment/locale/zh-cn';

// 设置 moment 的语言为中文
moment.locale('zh-cn');

createApp(App).use(store).use(router).use(Antd).mount('#app')
