<template>
  <div :id="identification" class="process-pie wh100"></div>
</template>

<script>
import { computed, defineComponent, onMounted, watch } from "vue";
import * as echarts from "echarts";
import "echarts/lib/chart/pie";

export default defineComponent({
  name: "Pie",
  components: {
    echarts,
  },
  props: {
    color: {
      type: Array,
      default: ["#73c764", "#e9443f", "#f9be35", "#bcbcbe"], // 正常 故障 异常 离线
    },
    basicData: {
      type: Array,
      default: () => [34, 20, 0, 30],
    },
    identification: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    let total = computed(
      () => props.basicData?.reduce((acc, curr) => acc + curr, 0) || 0
    );
    let myChart;
    watch(
      () => props.basicData,
      () => {
        if (myChart) {
          renderPie();
        }
      },
      { deep: true }
    );
    onMounted(() => {
      let chartDom = document.getElementById(props.identification);
      myChart = echarts.init(chartDom);
      renderPie();

      // window.addEventListener("resize", () => {
      // myChart.resize();
      // });
    });
    function renderPie() {
      let option = {
        series: [
          {
            type: "pie",
            // 设置饼图的半径
            radius: ["76%", "86%"],
            // 设置饼图的中心位置
            center: ["50%", "50%"],
            // 设置饼图的数据
            data: props.basicData,
            // 设置标签的显示属性
            label: {
              show: false,
            },
            color: props.color,

            // 设置图元的样式
            itemStyle: {
              // 设置图元的边框颜色
              borderColor: "#fff",
              // 设置图元的边框宽度
              borderWidth: 3,
            },
          },
        ],
        // 设置图表的标题
        title: [
          {
            // 圆环中间内容
            text: total.value,
            left: "center",
            top: "28%",
            textStyle: {
              color: "#fff",
              fontSize: 36,
              fontWeight: 400,
              align: "center",
            },
          },
          {
            text: "全 部",
            left: "center",
            top: "58%",
            textStyle: {
              fontSize: 17,
              align: "center",
              fontWeight: 400,
              color: "rgba(255, 255, 255, 0.8)",
            },
          },
        ],
      };
      myChart.setOption(option);
    }

    return {};
  },
});
</script>

<style lang="less" scoped>
</style>
