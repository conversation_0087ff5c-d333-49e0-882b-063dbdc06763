<template>
  <div id="vbar" class="wh100"></div>
</template>

<script setup>
import { nextTick, onMounted } from "vue";
import * as echarts from "echarts";

let option = {
  grid: {
    left: "1%",
    right: "3%",
    top: "20%",
    bottom: "10%",
    // 是否包含文本
    containLabel: true,
  },
  xAxis: [
    {
      type: "category",
      data: ["01", "02", "03", "04", "05", "06", "07", "08", "09"],
      axisTick: true,
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: "rgba(0, 0, 0, 0.6);",
        fontSize: 13,
        fontStyle: 500,
      },
    },
  ],
  yAxis: [
    {
      // min: 0,
      // interval: 10,
      type: "value",
      axisLine: {
        show: false,
      },
      splitLine: {
        // 设置 Y 轴的分割线
        show: true,
        lineStyle: {
          color: "#D2D4DA",
          width: 1,
          type: "dashed",
        },
      },
      axisLabel: {
        show: true,
        color: "rgba(0, 0, 0, 0.6);",
        fontSize: 13,
        fontStyle: 500,
      },
      name: "元",
      nameTextStyle: {
        color: "rgba(0, 0, 0, 0.6);",
        fontSize: 13,
        fontStyle: 500,
        align: "center", // 单位文本对齐方式
        padding: [0, 0, 0, -30],
      },
    },
  ],
  series: [
    {
      type: "bar",
      barWidth: 22,
      data: [30, 45, 90, 23, 41, 24, 12, 21, 31],
      z: 2,
      itemStyle: {
        color: "#936CF0",
        barBorderRadius: [10, 10, 0, 0],
      },
    },
  ],
};
onMounted(() => {
  nextTick(() => {
    let chartDom = document.getElementById("vbar");
    let myChart = echarts.init(chartDom);
    myChart.setOption(option);
  });
});
</script>