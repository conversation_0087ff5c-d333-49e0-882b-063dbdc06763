<template>
  <div id="hbar" class="wh100"></div>
</template>

<script setup>
import { nextTick, onMounted } from "vue";
import * as echarts from "echarts";

let option = {
  legend: {
    show: false,
  },
  grid: {
    left: "2%",
    right: "2%",
    top: "0%",
    bottom: "0%",
  },
  xAxis: [
    {
      splitLine: {
        show: false,
      },
      type: "value",
      show: false,
      axisLine: {
        //x轴坐标轴，false为隐藏，true为显示
        show: false,
      },
    },
  ],
  yAxis: [
    {
      show: true,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      type: "category",
      axisTick: {
        show: false,
      },
      inverse: true,
      axisLabel: {
        show: false,
      },
    },
    {
      type: "category",
      inverse: true,
      axisTick: "none",
      axisLine: "none",
      show: true,
      position: "right",
      axisLabel: {
        inside: true,
        verticalAlign: "bottom",
        padding: 5,
        margin: -5, //刻度标签与轴线之间的距离
        show: true,
        textStyle: {
          color: "#383838",
          fontSize: 24,
          fontWeight: 700,
        },
      },
      data: ["24", "20", "01", "01", "02"],
    },
  ],
  series: [
    {
      name: "背景",
      type: "bar",
      barWidth: 8,
      barGap: "-100%",
      data: [30, 30, 30, 30, 30],
      itemStyle: {
        normal: {
          color: "#F7F7F7",
          barBorderRadius: 4,
        },
      },
    },
    {
      show: true,
      name: "",
      type: "bar",
      data: [
        { name: "总计", value: "24", itemStyle: { color: "#165DFF" } },
        { name: "正常", value: "20", itemStyle: { color: "#73C764" } },
        { name: "异常", value: "01", itemStyle: { color: "#F9BE35" } },
        { name: "故障", value: "01", itemStyle: { color: "#E9443F" } },
        { name: "离线", value: "02", itemStyle: { color: "#BCBCBE" } },
      ],
      barWidth: 8, // 柱子宽度
      showBackground: false,
      label: {
        show: true,
        offset: [5, -20],
        color: "#808080",
        fontSize: 20,
        fontWeight: 500,
        position: "left",
        align: "left",
        formatter: function (params) {
          return params.data.name;
        },
      },
      itemStyle: {
        barBorderRadius: [4, 4, 4, 4],
      },
    },
  ],
};
onMounted(() => {
  nextTick(() => {
    let chartDom = document.getElementById("hbar");
    let myChart = echarts.init(chartDom);
    myChart.setOption(option);
  });
});
</script>