<template>
  <div class="wbar wh100 scroll">
    <div
      :id="props.data?.length == 3 ? 'wbar' : 'wbar-more'"
      class="w100"
      :style="`height: ${
        props.data?.length == 3
          ? '150px'
          : 'calc(50px + ' + props.data?.length * 40 + 'px)'
      }`"
    ></div>
  </div>
</template>

<script setup>
import { nextTick, onMounted } from "vue";
import * as echarts from "echarts";

let props = defineProps({
  legend: {
    type: Array,
    default: () => [],
  },
  data: {
    type: Array,
    default: () => [],
  },
});
let option = {
  grid: {
    left: "0%",
    top: "3%",
    right: "16%",
    bottom: "8%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    show: true,
    position: "bottom",
    axisTick: {
      show: false,
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "transparent",
        width: 2,
      },
    },
    axisLabel: {
      color: "rgba(0, 0, 0, 0.6)",
      margin: 5,
      fontSize: 13,
      fontStyle: 600,
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "#D2D4DA",
        width: 2,
        type: "dashed",
      },
    },
  },
  yAxis: {
    type: "category",
    axisTick: {
      show: false,
      alignWithLabel: false,
      length: 5,
    },
    splitLine: {
      //网格线
      show: false,
    },
    inverse: "true", //排序
    axisLine: {
      show: true,
      lineStyle: {
        color: "#D2D4DA",
        width: 2,
      },
    },

    axisLabel: {
      color: "#0D0C12",
      margin: 8,
      fontSize: 14,
    },
    data: props.legend,
  },
  series: [
    {
      name: "当日电站排名",
      type: "bar",
      label: {
        show: true,
        position: "right",
        formatter: "{c} kWh/kWp",
        color: "rgba(0, 0, 0, 0.6)",
        fontSize: 13,
        fontStyle: 600,
      },
      itemStyle: {
        show: true,
        color: "#2D8EFF",
        borderWidth: 0,
      },
      barWidth: 20,
      emphasis: {
        // 解决渐变色hover变色问题
        disabled: true,
      },
      data: props.data,
    },
  ],
};

let myChart = null;
onMounted(() => {
  nextTick(() => {
    let chartDom = document.getElementById(
      props.data?.length == 3 ? "wbar" : "wbar-more"
    );
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  });
});
</script>

<style lang="less" scoped>
.wbar {
  overflow-x: hidden;
}
</style>