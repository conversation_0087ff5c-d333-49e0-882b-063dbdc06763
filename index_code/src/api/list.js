import axios from "axios";
import router from '../router/index';
import { message } from "ant-design-vue";
import { computed } from "vue";

let locales = computed(() => JSON.parse(sessionStorage.getItem("locales")) || {})

const baseUrl = '/photovoltaic-manager/'

axios.defaults.withCredentials = true;

axios.interceptors.response.use(
    response => {
        if (typeof response?.data === "string" && response?.data?.indexOf('页面过期，请重新登录') > -1) {
            if (sessionStorage.getItem("userName")) message.warning(locales.value.chongxindl);
            sessionStorage.removeItem("userName");
            sessionStorage.removeItem("locales");
            router.replace("/login");
        }

        return response;
    },
    error => {
        return console.log(error, '-------------error');
    }
);

// 登录
export function login(data) {
    const params = new URLSearchParams();
    for (const key in data) {
        params.append(key, data[key]);
    }
    return axios.post(baseUrl + 'free/check.web', params, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    })
}
// 退出
export function logout(data) {
    return axios.get(baseUrl + 'free/logout.web', { params: data })
}

// 获取列表数据
export function getDataList(id, data) {
    return axios.get(baseUrl + id, {
        params: data,
    });
}

// 获取所有国家2-省地区3-城市4
export function getAllAreaData(data) {
    return axios.post(baseUrl + 'region/queryRegion.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    })
}

// 获取一些参数的可选项
export function queryDicList(dicId) {
    return axios.post(baseUrl + 'dic/queryDicList.web', { dicId }, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    })
}


// 新增/修改列表数据
export function saveOrUpdate(id, data) {
    return axios.post(baseUrl + id + '/saveOrUpdate.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 删除电站列表数据
export function deletePowerStation(data) {
    return axios.post(baseUrl + 'powerstation/deletePowerStationModel.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除采集器列表数据
export function deleteCloud(data) {
    return axios.post(baseUrl + 'cloudter/updateDeleteClouds.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除中继器列表
export function deleteRelay(data) {
    return axios.post(baseUrl + 'relay/deleteRelay.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除逆变器列表
export function deleteInverter(data) {
    return axios.post(baseUrl + 'inverter/updateDeleteInverterModels.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除组串列表
export function deleteComponentGroup(data) {
    return axios.post(baseUrl + 'componentGroup/updateDeleteComponentGroupModels.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除组串列表
export function deleteComponent(data) {
    return axios.post(baseUrl + 'component/updateDeleteComponentModels.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除警告列表
export function deleteWarnings(data) {
    return axios.post(baseUrl + 'warning/updateDeleteWarnings.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除版本列表
export function deleteVersion(data) {
    return axios.post(baseUrl + 'version/deleteVersion.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 删除升级任务列表
export function deleteUpdateTask(data) {
    return axios.post(baseUrl + 'updateTask/deleteUpdateTask.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 选择采集器
export function selectCloudTerminal(data) {
    return axios.post(baseUrl + 'cloudter/queryCloudTerminalList.web',
        data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 选择逆变器
export function selectInverter(data) {
    return axios.post(baseUrl + 'inverter/queryInverterList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 获取业主列表
export function getOwnerList(data) {
    return axios.post(baseUrl + 'memberCtr/queryMemberList.web?membertype=1,', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 选择业主
export function selectOwner(data) {
    return axios.get(baseUrl + 'powerstation/selectMemberInsert.web', {
        params: data,
    });
}
// 选择组件
export function selectComponent(data) {
    return axios.post(baseUrl + 'component/queryComponentList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 调试开关
export function debugSwitch(data) {
    return axios.get(baseUrl + 'powerstation/powerstationTryStatusTo.web', {
        params: data,
    });
}
// 选择设备
export function selectEquipment(data) {
    return axios.post(baseUrl + 'powerstation/selectEquipment.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 移除设备
export function stationDeleteEquipment(data) {
    return axios.post(baseUrl + 'powerstation/stationDeleteEquipment.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 升级任务-选择组件-获取组件
export function getTaskComponent(data) {
    return axios.post(baseUrl + 'updateTask/queryComponentList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 升级任务-选择版本-获取版本
export function getTaskVersion(data) {
    return axios.post(baseUrl + 'version/queryVersionList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 添加组件-获取组串列表
export function queryComponentGroupList(data) {
    return axios.post(baseUrl + 'componentGroup/queryComponentGroupList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 获取中继器列表
export function queryRelayListForCloud(data) {
    return axios.post(baseUrl + 'relay/queryRelayListForCloud.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 采集器导入 
export function cloudUpload(data) {
    return axios.post(baseUrl + 'cloudter/cloudUpload.htm', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 采集器-查询版本  queryType: 1-自身 2-组件
export function versionQueryTask(data) {
    return axios.post(baseUrl + 'versionQueryTask/createTask.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 采集器选择/移除组串
export function selectComponentGroup(data) {
    return axios.post(baseUrl + 'componentGroup/selectOrMoveCloudComponentGroup.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 采集器选择/移除中继器
export function selectRelay(data) {
    return axios.post(baseUrl + 'relay/selectOrMoveCloud.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 中继器选择组件
export function relayQueryComponent(data) {
    return axios.post(baseUrl + 'relay/queryComponentList.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 中继器选择/移除中继器
export function relayChangeComponent(data) {
    return axios.post(baseUrl + 'relay/changeComponent.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 逆变器导入 
export function inverterUpload(data) {
    return axios.post(baseUrl + 'inverter/inverterUpload.htm', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 逆变器选择/移除组串
export function inverterselectOrMoveComponentGroup(data) {
    return axios.post(baseUrl + 'componentGroup/selectOrMoveComponentGroup.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 逆变器选择/移除采集器
export function inverterselectOrMoveCloud(data) {
    return axios.post(baseUrl + 'inverter/selectOrMoveCloudTerminal.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 组串导入
export function componentGroupUpload(data) {
    return axios.post(baseUrl + 'componentGroup/componentGroupUpload.htm', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 组串选择/移除组件
export function selectOrMoveComponent(data) {
    return axios.post(baseUrl + 'component/selectOrMoveComponent.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 组件导入
export function componentUpload(data) {
    return axios.post(baseUrl + 'component/componentUpload.htm', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 警告列表-处理
export function batchUpdateWarning(data) {
    return axios.post(baseUrl + 'warning/batchUpdateWarning.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 版本列表-新增
export function versionSaveOrUpdate(data) {
    return axios.post(baseUrl + 'version/saveOrUpdate.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}



// 扫描下载APK
export function downloadApk(data) {
    return axios.post(baseUrl + 'downloadApk.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 修改密码
export function updatePassword(data) {
    return axios.get(baseUrl + 'user/changePassword.htm', { params: data }, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 查询任务列表-刷新
export function refreshQueryTask(data) {
    return axios.post(baseUrl + 'versionQueryTask/queryTask.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 查询任务列表-重试 
export function retryQueryTask(data) {
    return axios.post(baseUrl + 'versionQueryTask/retryTask.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 查询任务列表-查看查询结果 
export function queryTaskResult(data) {
    return axios.post(baseUrl + 'versionQueryTask/queryTaskResult.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 查询任务列表-结束任务
export function endTask(data) {
    return axios.post(baseUrl + 'versionQueryTask/endTask.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}



// 升级任务列表-重试
export function retryUpdateTask(data) {
    return axios.post(baseUrl + 'updateTask/updateTaskRetry.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 升级任务列表-结束任务
export function endUpdateTask(data) {
    return axios.post(baseUrl + 'updateTask/updateTaskEnd.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 升级任务列表-升级版本查询
export function versionQuery(data) {
    return axios.post(baseUrl + 'updateTask/versionQuery.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}
// 升级任务列表-查看升级结果
export function updateTaskResult(data) {
    return axios.post(baseUrl + 'updateTask/updateTaskResult.web', data, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 统计警告
export function queryWarningTypeCount(data) {
    return axios.get(baseUrl + 'warning/queryWarningTypeCount.web', {
        params: data
    }, {
        headers: {
            "content-type": "application/x-www-form-urlencoded",
        }
    });
}

// 电站发电量排名
export function groupByKwh(data) {
    return axios.get(baseUrl + 'component/groupByKwh.web', {
        params: data
    });
}

// 获取升级任务详情
export function getTaskDetails(data) {
    return axios.get(baseUrl + 'updateTask/getTaskDetails.web', {
        params: data
    });
}
