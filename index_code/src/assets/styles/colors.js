
// 项目颜色配置文件
export const colors = {
  // 主色调
  primary: 'rgba(22, 93, 255, 1)',
  primaryHover: 'rgba(22, 93, 255, 0.8)',
  primaryBackground: 'rgba(22, 93, 255, 0.1)',

  // 隆基红
  ljRed: '#E60012',
  ljRedHover: '#CC0010',
  ljRedBackground: 'rgba(230, 0, 18, 0.1)',

  // 文本色
  textDark: '#242424',        // 字体-深灰色
  textMedium: '#383838',      // 字体-浅灰色
  textGray: '#7D7D7D',        // 字体-灰色
  textLight: '#E5E5E5',       // 字体-浅灰色

  // 蓝色
  blue: '#165DFF',

  // 灰色系
  gray: '#7D7D7D',
  grayLight: '#F5F5F5',
  grayLighter: '#F2F2F2',

  // 绿色系
  green: '#49B02D',
  greenLight: '#73C764',

  // 红色系
  red: '#FF5733',
  redLight: '#C76363',


  // 黄色
  yellow: '#F9BE35',

  // 渐变色
  gradientBlue: 'linear-gradient(135deg, rgba(46, 143, 255, 1) 0%, rgba(81, 168, 255, 1) 45.14%, rgba(94, 177, 255, 1) 59.72%, rgba(112, 191, 255, 1) 100%)',

  // 渐变色的各个节点（用于单色场景）
  gradientBlueStart: 'rgba(46, 143, 255, 1)',
  gradientBlueMid: 'rgba(81, 168, 255, 1)',
  gradientBlueEnd: 'rgba(112, 191, 255, 1)',

  // 隆基红渐变色（新增设备按钮专用）
  gradientLjRed: 'linear-gradient(227.19deg, rgba(230, 0, 18, 1) 0%, rgba(255, 255, 255, 1) 100%)',

  // 背景色
  background: '#ffffff',
  backgroundGray: '#F5F5F5',

  // 状态色
  statusProcessed: 'rgba(73, 176, 45, 1)',
  statusProcessedBg: 'rgba(115, 199, 100, 0.12)',
  statusProcessedBorder: 'rgba(115, 199, 100, 0.12)',
  statusUnprocessed: 'rgba(255, 87, 51, 1)',
  statusUnprocessedBg: 'rgba(199, 99, 99, 0.12)',
  statusUnprocessedBorder: 'rgba(199, 99, 99, 0.12)',
};

// 常用的颜色组合
export const colorSchemes = {
  // 操作按钮颜色方案
  actionButtons: {
    primary: colors.primary,
    primaryHover: colors.primaryHover,
    danger: colors.danger,
    dangerHover: colors.dangerHover
  },

  // 状态按钮颜色方案
  statusButtons: {
    unprocessed: {
      color: colors.statusUnprocessed,
      background: colors.statusUnprocessedBg,
      border: colors.statusUnprocessedBorder
    },
    processed: {
      color: colors.statusProcessed,
      background: colors.statusProcessedBg,
      border: colors.statusProcessedBorder
    }
  }
};
