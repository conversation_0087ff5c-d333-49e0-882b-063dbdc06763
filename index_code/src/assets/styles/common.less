/* 基础样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

/* 通用工具类 */
.wh100 {
  width: 100%;
  height: 100%;
}

/* 布局相关 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

/* 间距 */
.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

/* 其他通用样式 */

div[data-v-eff066d8] {
    color: #12ccc6
}

.overview>.top[data-v-00b0e354] {
    height: 270px;
    padding: 20px 24px;
    background: #fff;
    border-radius: 6px;
    overflow: hidden
}

.overview>.top .header .topic>img[data-v-00b0e354] {
    width: 42px;
    height: 42px
}

.overview>.top .header .topic .title[data-v-00b0e354] {
    font-size: 20px;
    font-weight: 500;
    color: #0d0c12
}

.overview>.top .header .topic .status[data-v-00b0e354] {
    position: relative;
    font-size: 16px;
    color: #43cf7c;
    line-height: 22px
}

.overview>.top .header .topic .status[data-v-00b0e354]:before {
    position: absolute;
    top: 7px;
    left: 0;
    content: "";
    width: 8px;
    height: 8px;
    background-color: #43cf7c;
    border-radius: 50%
}

.overview>.top .header>.info .l>img[data-v-00b0e354] {
    height: 33px
}

.overview>.top .header>.info .l>span[data-v-00b0e354] {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #383838
}

.overview>.top .header>.info .r .date[data-v-00b0e354] {
    font-size: 12px;
    font-weight: 500;
    line-height: 17px;
    color: grey
}

.overview>.top .header>.info .r .temperature[data-v-00b0e354] {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: #165dff;
    margin-top: 2px
}

.overview>.top .container[data-v-00b0e354] {
    margin-top: 42px
}

.overview>.top .container .info-item[data-v-00b0e354] {
    width: 359px;
    height: 120px;
    padding: 13px 0;
    margin-left: 16px;
    border-radius: 10px;
    background: #f7f7f7
}

.overview>.top .container .info-item[data-v-00b0e354]:first-child {
    margin-left: 0
}

.overview>.top .container .info-item>img[data-v-00b0e354] {
    width: 90px;
    height: 94px;
    margin: 0 32px
}

.overview>.top .container .info-item .detail .value[data-v-00b0e354] {
    font-size: 32px;
    font-weight: 700;
    color: #383838;
    line-height: 44px;
    margin-right: 5px
}

.overview>.top .container .info-item .detail .unit[data-v-00b0e354] {
    font-size: 20px;
    color: #383838;
    line-height: 36px
}

.overview>.top .container .info-item .detail .label[data-v-00b0e354] {
    margin-top: 2px;
    font-size: 24px;
    line-height: 33px;
    color: #a6a6a6
}

.overview .main[data-v-00b0e354] {
    display: grid;
    grid-template-columns: repeat(2,1fr);
    grid-template-rows: repeat(2,1fr);
    grid-gap: 24px;
    margin-top: 23px
}

.overview .main>div[data-v-00b0e354] {
    height: 558px;
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden
}

.overview .main .left1[data-v-00b0e354] {
    position: relative;
    padding: 28px 24px
}

.overview .main .left1 .title[data-v-00b0e354] {
    position: absolute;
    left: 24px;
    top: 28px;
    width: 109px;
    height: 42px;
    border-radius: 21px;
    background: #f7f7f7
}

.overview .main .left1 .title>img[data-v-00b0e354] {
    width: 20px;
    height: 20px;
    margin-right: 8px
}

.overview .main .left1 .title>span[data-v-00b0e354] {
    font-size: 16px;
    font-weight: 500;
    color: grey
}

.overview .main .left1 .part1[data-v-00b0e354] {
    position: relative;
    width: 466px;
    height: 466px;
    padding-top: 34px
}

.overview .main .left1 .part1 .circle[data-v-00b0e354] {
    position: relative;
    width: 122px;
    height: 122px;
    border-radius: 50%
}

.overview .main .left1 .part1 .circle>div[data-v-00b0e354] {
    width: 100px;
    height: 100px;
    background: #fff;
    border-radius: 50%
}

.overview .main .left1 .part1 .circle>div .label[data-v-00b0e354] {
    position: absolute;
    bottom: -48px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: #383838
}

.overview .main .left1 .part1 .circle>div .value[data-v-00b0e354] {
    font-size: 16px;
    font-weight: 700;
    color: #383838;
    margin: 2px 0
}

.overview .main .left1 .part1 .circle>div .unit[data-v-00b0e354] {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #a6a6a6
}

.overview .main .left1 .part1 .circle>div>img[data-v-00b0e354] {
    width: 32px;
    height: 32px
}

.overview .main .left1 .part1 .top[data-v-00b0e354] {
    height: 122px
}

.overview .main .left1 .part1 .top .circle[data-v-00b0e354] {
    border: 1px solid rgba(255,195,0,.37)
}

.overview .main .left1 .part1 .top .circle>div[data-v-00b0e354] {
    border: 1px solid rgba(255,195,0,.8);
    box-shadow: 0 0 5px 2px rgba(255,195,0,.23)
}

.overview .main .left1 .part1 .top .circle>div .label[data-v-00b0e354] {
    top: -48px;
    bottom: unset
}

.overview .main .left1 .part1 .bottom[data-v-00b0e354] {
    height: 122px
}

.overview .main .left1 .part1 .bottom .circle[data-v-00b0e354] {
    border: 1px solid rgba(42,130,228,.37)
}

.overview .main .left1 .part1 .bottom .circle>div[data-v-00b0e354] {
    border: 1px solid rgba(42,130,228,.62);
    box-shadow: 0 0 5px 2px rgba(42,130,228,.32)
}

.overview .main .left1 .part1 .center[data-v-00b0e354] {
    height: 122px;
    margin: 16px 0
}

.overview .main .left1 .part1 .center .circle.l[data-v-00b0e354] {
    border: 1px solid rgba(0,186,173,.37)
}

.overview .main .left1 .part1 .center .circle.l>div[data-v-00b0e354] {
    border: 1px solid rgba(0,186,173,.62);
    box-shadow: 0 0 5px 2px rgba(0,186,173,.32)
}

.overview .main .left1 .part1 .center .circle.r[data-v-00b0e354] {
    border: 1px solid rgba(172,51,193,.37)
}

.overview .main .left1 .part1 .center .circle.r>div[data-v-00b0e354] {
    border: 1px solid rgba(172,51,193,.8);
    box-shadow: 0 0 5px 2px rgba(172,51,193,.19)
}

.overview .main .left1 .part1 .line1[data-v-00b0e354] {
    position: absolute;
    top: 145px;
    left: calc(50% - 1px);
    width: 2px;
    height: 176px;
    background: linear-gradient(180deg,#ffcf33,#f2fa9b 50.76%,#7bb2ee)
}

.overview .main .left1 .part1 .line1[data-v-00b0e354]:before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    content: "";
    border-top: 9px solid #ffc300;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent
}

.overview .main .left1 .part1 .line2[data-v-00b0e354] {
    position: absolute;
    top: 142px;
    left: 111px;
    width: calc(50% - 124px);
    height: 100px;
    border: 2px solid;
    -o-border-image: linear-gradient(225deg,#fed23b,#61d4cc) 2;
    border-image: linear-gradient(225deg,#fed23b,#61d4cc) 2;
    border-top: none;
    border-left: none;
    border-bottom-right-radius: 14px;
    background: transparent;
    border-radius: 0 0 14px 0
}

.overview .main .left1 .part1 .line2[data-v-00b0e354]:before {
    position: absolute;
    top: 40px;
    right: -9px;
    content: "";
    border-top: 9px solid #6ac9d7;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    z-index: 1
}

.overview .main .left1 .part1 .line3[data-v-00b0e354] {
    position: absolute;
    top: 236px;
    right: 111px;
    width: calc(50% - 124px);
    height: 87px;
    border: 2px solid;
    -o-border-image: linear-gradient(205deg,#bd5ccd,#7eaeed,#7bb2ee) 2;
    border-image: linear-gradient(205deg,#bd5ccd,#7eaeed,#7bb2ee) 2;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 14px
}

.overview .main .left1 .part1 .line3[data-v-00b0e354]:before {
    position: absolute;
    top: 34px;
    left: -9px;
    content: "";
    border-top: 9px solid #a976d7;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent
}

.overview .main .left1 .part2[data-v-00b0e354] {
    padding-top: 50px
}

.overview .main .left1 .part2 .item[data-v-00b0e354] {
    margin-bottom: 36px
}

.overview .main .left1 .part2 .item .value[data-v-00b0e354] {
    font-size: 32px;
    font-weight: 700;
    color: #383838;
    line-height: 44px;
    margin-right: 5px
}

.overview .main .left1 .part2 .item .unit[data-v-00b0e354] {
    font-size: 20px;
    color: #383838;
    line-height: 36px
}

.overview .main .left1 .part2 .item .label[data-v-00b0e354] {
    margin-top: 2px;
    font-size: 24px;
    line-height: 33px;
    color: #a6a6a6
}

.overview .main .right1[data-v-00b0e354] {
    padding: 59px 48px 50px
}

.overview .main .right1 .info[data-v-00b0e354] {
    margin-left: 80px;
    padding: 15px 0 40px
}

.overview .main .right1 .info>div .label[data-v-00b0e354] {
    font-size: 16px;
    color: #0d0c12;
    font-weight: 600
}

.overview .main .right1 .info>div .value[data-v-00b0e354] {
    font-size: 16px;
    color: #0d0c12;
    margin-top: 30px
}

.login-view[data-v-be406f7e] {
    position: relative;
    background-size: cover;
    animation: backgroundFade-be406f7e 6s infinite
}

@keyframes backgroundFade-be406f7e {
    0%,to {
        background-image: url(../images/login-bg-1.png)
    }

    50% {
        background-image: url(../images/login-bg-2.png)
    }
}

.login-view .login-modal[data-v-be406f7e] {
    position: absolute;
    left: 50%;
    top: 150px;
    transform: translateX(-50%)
}

.login-view .login-modal .title[data-v-be406f7e] {
    margin-bottom: 25px;
    color: #fff;
    font-size: 30px;
    font-weight: 700;
    text-shadow: 0 1px 4px rgba(0,0,0,.2)
}

.login-view .login-modal[data-v-be406f7e] .ant-form {
    width: 302px
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input {
    width: 100%;
    height: 42px;
    font-size: 14px;
    color: #fff;
    background: rgba(45,45,45,.15);
    border-radius: 6px;
    border: 1px solid hsla(0,0%,100%,.15);
    text-shadow: 0 1px 2px rgba(0,0,0,.1);
    overflow: hidden
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password::-moz-placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector::-moz-placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input::-moz-placeholder {
    font-size: 14px;
    color: #fff
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password::placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector::placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input::placeholder {
    font-size: 14px;
    color: #fff
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password.ant-input-affix-wrapper-focused,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password:focus,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector.ant-input-affix-wrapper-focused,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector:focus,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input.ant-input-affix-wrapper-focused,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input:focus {
    box-shadow: inset 0 2px 3px 0 rgba(0,0,0,.1);
    border: 1px solid hsla(0,0%,100%,.15)
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password .ant-select-selection-item,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector .ant-select-selection-item,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input .ant-select-selection-item {
    line-height: 40px!important
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector>.ant-input,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input>.ant-input {
    padding: 4px 11px;
    font-size: 14px;
    color: #fff;
    border: none;
    background: transparent
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input::-moz-placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector>.ant-input::-moz-placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input>.ant-input::-moz-placeholder {
    font-size: 14px;
    color: #fff
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input::placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector>.ant-input::placeholder,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input>.ant-input::placeholder {
    font-size: 14px;
    color: #fff
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password.ant-input-affix-wrapper,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector.ant-input-affix-wrapper,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input.ant-input-affix-wrapper {
    padding: 0
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password .ant-input-suffix,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector .ant-input-suffix,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input .ant-input-suffix {
    display: none
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-input-password .ant-input-suffix>.anticon,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content .ant-select-selector .ant-input-suffix>.anticon,.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>.ant-input .ant-input-suffix>.anticon {
    color: #fff
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>button {
    width: 300px;
    height: 44px;
    font-size: 16px;
    color: #fff;
    background: #ef4300;
    border: none
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>button:active {
    opacity: .5
}

.login-view .login-modal[data-v-be406f7e] .ant-form .ant-form-item-control-input-content>button:hover {
    color: #fff
}

#app {
    width: 100%;
    height: 100%;
    min-width: 1280px;
    overflow-x: auto
}

#nav {
    background-color: #f7f8fa
}

#nav .top-header {
    height: 60px
}

#nav .top-header .system-icon {
    width: 240px;
    padding-top: 0;
    background-color: #f5faff;
    box-shadow: 0 4px 4px rgba(0,0,0,.125),0 12px 16px rgba(13,13,18,.08)
}

#nav .top-header .system-info {
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0,0,0,.05)
}

#nav .top-header .system-info .time {
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap
}

#nav .main-container {
    height: calc(100% - 60px)
}

#nav .main-container .menu-info {
    padding: 10px 14px 20px 12px;
    background-color: #f5faff;
    box-shadow: 0 4px 4px rgba(0,0,0,.125),0 12px 16px rgba(13,13,18,.08)
}

#nav .main-container .menu-info .menu-info-item {
    height: 44px;
    padding: 0 12px;
    cursor: pointer
}

#nav .main-container .menu-info .menu-info-item img {
    width: 18px;
    height: 18px
}

#nav .main-container .menu-info .ant-menu {
    width: 214px;
    background-color: transparent;
    border-inline-end:unset}

#nav .main-container .menu-info .ant-menu .ant-menu-item {
    padding: 0 12px!important
}

#nav .main-container .menu-info .ant-menu .ant-menu-item .ant-menu-title-content {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #818898
}

#nav .main-container .menu-info .ant-menu .ant-menu-item.ant-menu-item-selected {
    background: #fff;
    border: 1px solid #dfe1e7
}

#nav .main-container .menu-info .ant-menu .ant-menu-item.ant-menu-item-selected .ant-menu-title-content {
    color: #0d0c12
}

#nav .main-container .menu-info .ant-menu .ant-menu-submenu .ant-menu-submenu-title {
    padding: 0 12px!important
}

#nav .main-container .menu-info .ant-menu .ant-menu-submenu .ant-menu-submenu-title .ant-menu-title-content {
    margin-left: 0;
    color: #818898
}

#nav .main-container .menu-info .ant-menu .ant-menu-submenu.ant-menu-submenu-selected .ant-menu-submenu-title .ant-menu-title-content,#nav .main-container .menu-info .ant-menu .ant-menu-submenu .ant-menu-submenu-title .ant-menu-submenu-arrow {
    color: #0d0c12
}

#nav .main-container .menu-info .ant-menu.ant-menu-inline-collapsed {
    width: 50px
}

#nav .main-container .menu-info .ant-menu.ant-menu-sub .ant-menu-item {
    padding-left: 38px!important
}

#nav .main-container .right-sider {
    width: calc(100% - 240px);
    padding-bottom: 24px
}

#nav .main-container .right-sider .nav-part {
    max-height: 100%;
    overflow-y: auto
}

.apk-modal {
    width: 380px!important
}

.apk-modal .modal-box {
    padding: 10px 0 0!important
}

.apk-modal .ant-modal-footer {
    display: none!important
}

.password-modal {
    width: 600px!important
}

.password-modal .modal-box {
    width: 500px!important;
    padding: 20px 0 0!important
}

.password-modal .ant-form {
    width: 480px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password {
    width: 100%;
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password::-moz-placeholder {
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password::placeholder {
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input {
    padding: 4px 11px;
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input::-moz-placeholder {
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password>.ant-input::placeholder {
    font-size: 14px
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password.ant-input-affix-wrapper {
    padding: 0
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password .ant-input-suffix {
    display: none
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-input-password .ant-input-suffix>.anticon {
    color: #fff
}

.password-modal .ant-form .ant-form-item-control-input-content .ant-form-item {
    display: flex!important;
    flex-direction: row!important;
    justify-content: flex-start!important;
    align-items: center!important
}

.password-modal .ant-modal-footer {
    display: none!important
}

.flex,.flex-wrap {
    box-sizing: border-box;
    display: flex
}

.flex-wrap {
    flex-wrap: wrap
}

.flex1 {
    box-sizing: border-box;
    flex: 1
}

.flex-row,.flex-row-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: row
}

.flex-row-start {
    align-items: flex-start;
    justify-content: flex-start
}

.flex-row-start-start {
    justify-content: flex-start
}

.flex-row-start-center,.flex-row-start-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-start
}

.flex-row-start-center {
    justify-content: center
}

.flex-row-start-end {
    justify-content: flex-end
}

.flex-row-start-between,.flex-row-start-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-start
}

.flex-row-start-between {
    justify-content: space-between
}

.flex-row-start-around {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-around
}

.flex-row-center-start {
    justify-content: flex-start
}

.flex-row-center-center,.flex-row-center-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center
}

.flex-row-center-center {
    justify-content: center
}

.flex-row-center-end {
    justify-content: flex-end
}

.flex-row-center-between,.flex-row-center-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center
}

.flex-row-center-between {
    justify-content: space-between
}

.flex-row-center-around {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around
}

.flex-row-end-start {
    justify-content: flex-start
}

.flex-row-end-center,.flex-row-end-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-end
}

.flex-row-end-center {
    justify-content: center
}

.flex-row-end,.flex-row-end-end {
    justify-content: flex-end
}

.flex-row-end,.flex-row-end-around,.flex-row-end-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-end
}

.flex-row-end-around {
    justify-content: space-around
}

.flex-row-end-between {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between
}

.flex-column,.flex-column-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: column
}

.flex-column-start {
    justify-content: flex-start;
    align-items: flex-start
}

.flex-column-start-start {
    justify-content: flex-start
}

.flex-column-start-center,.flex-column-start-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

.flex-column-start-center {
    justify-content: center
}

.flex-column-start-end {
    justify-content: flex-end
}

.flex-column-start-between,.flex-column-start-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

.flex-column-start-between {
    justify-content: space-between
}

.flex-column-start-around {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-around
}

.flex-column-center,.flex-column-center-start {
    justify-content: flex-start
}

.flex-column-center,.flex-column-center-center,.flex-column-center-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center
}

.flex-column-center-center {
    justify-content: center
}

.flex-column-center-end {
    justify-content: flex-end
}

.flex-column-center-between,.flex-column-center-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center
}

.flex-column-center-between {
    justify-content: space-between
}

.flex-column-center-around {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around
}

.flex-column-end-start {
    justify-content: flex-start
}

.flex-column-end-center,.flex-column-end-start {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.flex-column-end-center {
    justify-content: center
}

.flex-column-end,.flex-column-end-end {
    justify-content: flex-end
}

.flex-column-end,.flex-column-end-between,.flex-column-end-end {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.flex-column-end-between {
    justify-content: space-between
}

.flex-column-end-around {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-around
}

.nowrap {
    white-space: nowrap
}

[disabled] {
    cursor: not-allowed;
    opacity: .4
}

.wh100 {
    height: 100%
}

.w100,.wh100 {
    width: 100%
}

.h100 {
    height: 100%
}

.p16 {
    padding: 16px
}

.p16,.pt16 {
    box-sizing: border-box
}

.pt16 {
    padding-top: 16px
}

.pb16,.ptb16 {
    padding-bottom: 16px;
    box-sizing: border-box
}

.ptb16 {
    padding-top: 16px
}

.pl16 {
    padding-left: 16px;
    box-sizing: border-box
}

.plr16,.pr16 {
    padding-right: 16px;
    box-sizing: border-box
}

.plr16 {
    padding-left: 16px
}

.m16 {
    margin: 16px
}

.m16,.mt16 {
    box-sizing: border-box
}

.mt16 {
    margin-top: 16px
}

.mb16,.mtb16 {
    margin-bottom: 16px;
    box-sizing: border-box
}

.mtb16 {
    margin-top: 16px
}

.ml16 {
    margin-left: 16px;
    box-sizing: border-box
}

.mlr16,.mr16 {
    margin-right: 16px;
    box-sizing: border-box
}

.mlr16 {
    margin-left: 16px
}

.cursor {
    cursor: pointer
}

.scroll {
    overflow-x: auto;
    overflow-y: auto
}

.scroll::-webkit-scrollbar {
    width: 6px!important;
    height: 6px!important;
    border-radius: 3px!important
}

.scroll::-webkit-scrollbar-track {
    border-radius: 3px!important;
    background-color: transparent!important
}

.scroll::-webkit-scrollbar-thumb {
    border-radius: 3px!important;
    background: #cce4ff!important
}

.table-part {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 54px);
    padding: 16px 24px;
    border-radius: 6px;
    background-color: #fff;
    overflow: hidden
}

button {
    outline: none
}

p {
    margin: 0
}

i {
    font-style: normal
}

.btn {
    min-width: 84px;
    height: 32px;
    border-radius: 2px;
    background: #165dff;
    font-size: 14px;
    color: #fff;
    border: none;
    cursor: pointer
}

.btn .anticon {
    font-size: 12px;
    color: #fff;
    margin-right: 10px
}

.btn>span {
    font-size: 14px;
    color: #fff
}

.btn.btn-reset {
    background: #f2f3f5;
    color: #1d2129
}

.btn.btn-reset .anticon {
    color: #4e5969
}

.btn.btn-reset>span {
    color: #1d2129
}

.btn.btn-delete {
    background: #fc6f6a
}

.btn:active {
    opacity: .5
}

.btn-special {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: -moz-fit-content;
    width: fit-content;
    padding: 0 20px;
    height: 32px;
    background-image: url(../images/btn-bg.png);
    background-repeat: no-repeat; 
    background-size: 100% 100%;
    cursor: pointer
}

.btn-special>span {
    display: inline-block;
    font-size: 13px;
    font-weight: 700;
    transform: skewX(-5deg);
    margin-bottom: 3px;
    background: linear-gradient(181.24deg,#fff,#aef5ff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ant-breadcrumb>ol>li .ant-breadcrumb-link {
    font-size: 14px
}

.ant-breadcrumb>ol>li:active .ant-breadcrumb-link {
    color: #165dff
}

.ant-breadcrumb>ol>li:last-child .ant-breadcrumb-link {
    color: rgba(78,89,105,.5)!important
}

.ant-cascader-menu {
    min-width: 118px!important;
    padding: 0!important;
    border: 1px solid #e2e2e2!important;
    overflow-y: auto
}

.ant-cascader-menu .ant-cascader-menu-item {
    font-size: 12px!important;
    color: #0d0c12!important;
    background-color: #fff!important
}

.ant-cascader-menu .ant-cascader-menu-item.ant-cascader-menu-item-active {
    color: #169bfa!important;
    background: rgba(22,155,250,.1)!important
}

.ant-cascader-menu .ant-cascader-menu-item .ant-cascader-menu-item-expand-icon {
    color: #0d0c12!important
}

.ant-cascader-menu::-webkit-scrollbar {
    width: 6px!important;
    height: 6px!important;
    border-radius: 3px!important
}

.ant-cascader-menu::-webkit-scrollbar-track {
    border-radius: 3px!important;
    background-color: transparent!important
}

.ant-cascader-menu::-webkit-scrollbar-thumb {
    border-radius: 3px!important;
    background: #cce4ff!important
}


.wbar[data-v-09903aac] {
    overflow-x: hidden
}

.home[data-v-2588cccf] {
    position: relative
}

.home .icons[data-v-2588cccf] {
    position: absolute;
    top: 38px;
    right: 0;
    width: 52px;
    height: 144px;
    z-index: 1000
}

.home .icons>div[data-v-2588cccf] {
    position: relative;
    width: 32px;
    height: 32px;
    cursor: pointer
}

.home .icons>div>img[data-v-2588cccf] {
    width: 32px;
    height: 32px
}

.home .icons>div.active[data-v-2588cccf]:before {
    position: absolute;
    top: 10px;
    right: -18px;
    content: "";
    border-right: 8px solid #fff;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent
}

.home .station-info[data-v-2588cccf] {
    flex-shrink: 0;
    height: 367px;
    padding-right: 52px;
    background-image: url(../images/system-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat
}

.home .station-info .title[data-v-2588cccf] {
    color: #fff;
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 1px
}

.home .station-info .kpi-grid[data-v-2588cccf] {
    display: grid;
    grid-template-columns: repeat(2,1fr);
    grid-column-gap: 70px;
    grid-row-gap: 20px;
    margin-top: 40px
}

.home .station-info .kpi-grid .grid-item>img[data-v-2588cccf] {
    width: 32px;
    height: 32px;
    margin: 8px 16px 0 0
}

.home .station-info .kpi-grid .grid-item .info .top[data-v-2588cccf] {
    margin-bottom: 6px
}

.home .station-info .kpi-grid .grid-item .info .top .value[data-v-2588cccf] {
    width: 70px;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1px;
    line-height: 28px;
    color: #fff;
    margin-bottom: 2px
}

.home .station-info .kpi-grid .grid-item .info .top .unit[data-v-2588cccf] {
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 17px;
    color: #a6a6a6
}

.home .station-info .kpi-grid .grid-item .info .label[data-v-2588cccf] {
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 20px;
    color: #808c9d
}

.home .station-info .alarm-part .detail[data-v-2588cccf],.home .station-info .state-part .detail[data-v-2588cccf] {
    height: 208px;
    margin-top: 40px
}

.home .station-info .alarm-part .detail .left[data-v-2588cccf],.home .station-info .state-part .detail .left[data-v-2588cccf] {
    width: 144px;
    height: 144px;
    margin-right: 80px
}

.home .station-info .alarm-part .detail .right .state-item[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item[data-v-2588cccf] {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 24px
}

.home .station-info .alarm-part .detail .right .state-item[data-v-2588cccf]:last-child,.home .station-info .state-part .detail .right .state-item[data-v-2588cccf]:last-child {
    margin-bottom: 0
}

.home .station-info .alarm-part .detail .right .state-item .type[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item .type[data-v-2588cccf] {
    width: 100px;
    font-size: 16.8px;
    line-height: 24px;
    color: #fff
}

.home .station-info .alarm-part .detail .right .state-item .num[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item .num[data-v-2588cccf] {
    font-size: 20px;
    font-weight: 700;
    line-height: 26.4px;
    color: #fff
}

.home .station-info .alarm-part .detail .right .state-item .circle[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item .circle[data-v-2588cccf] {
    width: 16px;
    height: 16px;
    margin-right: 2px;
    margin-bottom: 2px;
    border-radius: 50%;
    background-color: hsla(0,0%,100%,.29)
}

.home .station-info .alarm-part .detail .right .state-item .circle>div[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item .circle>div[data-v-2588cccf] {
    width: 10px;
    height: 10px;
    border-radius: 50%
}

.home .station-info .alarm-part .detail .right .state-item.normal .circle>div[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item.normal .circle>div[data-v-2588cccf] {
    background-color: #73c764
}

.home .station-info .alarm-part .detail .right .state-item.fault .circle>div[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item.fault .circle>div[data-v-2588cccf] {
    background-color: #e9443f
}

.home .station-info .alarm-part .detail .right .state-item.abnormal .circle>div[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item.abnormal .circle>div[data-v-2588cccf] {
    background-color: #f9be35
}

.home .station-info .alarm-part .detail .right .state-item.offline .circle>div[data-v-2588cccf],.home .station-info .state-part .detail .right .state-item.offline .circle>div[data-v-2588cccf] {
    background-color: #bcbcbe
}

.home .charts-info[data-v-2588cccf] {
    height: 414px;
    overflow: hidden
}

.home .charts-info .right .bottom[data-v-2588cccf],.home .charts-info .right .top[data-v-2588cccf] {
    height: 214px;
    background-color: #fff;
    padding: 20px 24px 0;
    border-radius: 2px;
    overflow: hidden
}

.home .charts-info .right .bottom .title[data-v-2588cccf],.home .charts-info .right .top .title[data-v-2588cccf] {
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    color: #0d0c12
}

.home .charts-info .right .bottom .cards[data-v-2588cccf],.home .charts-info .right .top .cards[data-v-2588cccf] {
    margin-top: 12px
}

.home .charts-info .right .bottom .cards .card-item[data-v-2588cccf],.home .charts-info .right .top .cards .card-item[data-v-2588cccf] {
    height: 110px;
    padding: 0 24px;
    background-image: url(../images/bg2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat
}

.home .charts-info .right .bottom .cards .card-item .value[data-v-2588cccf],.home .charts-info .right .top .cards .card-item .value[data-v-2588cccf] {
    font-size: 28px;
    color: #0d0c12;
    line-height: 44px
}

.home .charts-info .right .bottom .cards .card-item .unit[data-v-2588cccf],.home .charts-info .right .top .cards .card-item .unit[data-v-2588cccf] {
    font-size: 18px;
    color: #0d0c12;
    margin-left: 5px;
    line-height: 40px
}

.home .charts-info .right .bottom .cards .card-item .label[data-v-2588cccf],.home .charts-info .right .top .cards .card-item .label[data-v-2588cccf] {
    font-size: 16px;
    color: #a6a6a6;
    margin-top: 5px
}

.home .charts-info .right .bottom .cards .card-item[data-v-2588cccf]:first-child,.home .charts-info .right .top .cards .card-item[data-v-2588cccf]:first-child {
    background-image: url(../images/bg1.png);
    background-size: 100% 100%;
    background-repeat: no-repeat
}

.home .charts-info .right .top[data-v-2588cccf] {
    flex-shrink: 0
}

.home .table-container[data-v-2588cccf] {
    max-height: 100%
}

.home .table-part[data-v-2588cccf] {
    height: 100%!important
}

.home .table-part .btns[data-v-2588cccf] {
    margin: 12px 0
}

.home .table-part .btns>button[data-v-2588cccf] {
    min-width: 84px;
    height: 32px;
    border-radius: 2px;
    background: #165dff;
    font-size: 14px;
    color: #fff;
    border: none;
    cursor: pointer
}

.home .table-part .btns>button[data-v-2588cccf]:active {
    opacity: .5
}

.station-modal .ant-modal-footer {
    display: none!important
}

.station-modal .ant-modal-body {
    height: 266px!important;
    padding-top: 16px!important
}

.column-title[data-v-7d243f36] {
    display: flex;
    flex: 1 1 0%;
    justify-content: space-between;
    align-items: center;
    color: rgb(13, 12, 18);
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
}

.search-border[data-v-66f82878] {
    flex-shrink: 0;
    padding-bottom: 24px;
    border-bottom: 1px solid rgb(229, 229, 229);
    overflow: hidden;
}

.search-border .title[data-v-66f82878] {
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    color: rgb(29, 33, 41);
    margin-bottom: 20px;
}

.search-border .left[data-v-66f82878] {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px 28px;
    height: fit-content;
}

.search-border .left .search-list[data-v-66f82878] {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.search-border .left .search-list .label[data-v-66f82878] {
    flex-shrink: 0;
    width: fit-content;
    margin-right: 16px;
    font-size: 14px;
    color: rgb(13, 12, 18);
    text-align: right;
    white-space: nowrap;
}

.search-border .left .search-list[data-v-66f82878] .ant-input, .search-border .left .search-list[data-v-66f82878] .ant-picker-range, .search-border .left .search-list[data-v-66f82878] .ant-select {
    flex-shrink: 0;
    border: none;
    background: rgb(247, 248, 250);
    font-size: 14px;
    color: rgb(134, 144, 156);
    width: 256px !important;
    height: 32px !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input::placeholder, .search-border .left .search-list[data-v-66f82878] .ant-picker-range::placeholder, .search-border .left .search-list[data-v-66f82878] .ant-select::placeholder {
    font-size: 14px;
    color: rgb(134, 144, 156);
}

.search-border .left .search-list[data-v-66f82878] .ant-input:focus, .search-border .left .search-list[data-v-66f82878] .ant-picker-range:focus, .search-border .left .search-list[data-v-66f82878] .ant-select:focus {
    box-shadow: none;
    border: 1px solid rgb(22, 155, 250);
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-select-selector, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-select-selector, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-select-selector {
    border: none;
    background: rgb(247, 248, 250);
    font-size: 14px;
    color: rgb(134, 144, 156);
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-select-selector .ant-select-selection-placeholder, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-select-selector .ant-select-selection-placeholder, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-select-selector .ant-select-selection-placeholder {
    font-size: 14px;
    color: rgb(134, 144, 156);
}

.search-border .left .search-list[data-v-66f82878] .ant-input.ant-select-focused .ant-select-selector, .search-border .left .search-list[data-v-66f82878] .ant-picker-range.ant-select-focused .ant-select-selector, .search-border .left .search-list[data-v-66f82878] .ant-select.ant-select-focused .ant-select-selector {
    box-shadow: none !important;
    border: 1px solid rgb(22, 155, 250) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-select-arrow, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-select-arrow, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-select-arrow {
    color: rgb(78, 89, 105) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-input input, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-input input, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-input input {
    font-size: 14px !important;
    color: rgb(134, 144, 156) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-input input::placeholder, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-input input::placeholder, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-input input::placeholder {
    font-size: 14px !important;
    color: rgb(134, 144, 156) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-suffix, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-suffix, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-suffix {
    font-size: 12px !important;
    color: rgb(78, 89, 105) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-active-bar, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-active-bar, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-active-bar {
    display: none;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-disabled, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-disabled, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-disabled {
    font-size: 12px;
    color: rgb(191, 191, 191);
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-disabled::before, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-disabled::before, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-disabled::before {
    background-color: transparent;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-disabled.ant-picker-cell-in-view, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-disabled.ant-picker-cell-in-view, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-disabled.ant-picker-cell-in-view {
    color: rgb(13, 12, 18);
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-range-end .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-range-start .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-range-end .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-range-start .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-range-end .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-range-start .ant-picker-cell-inner {
    background: rgba(22, 155, 250, 0.1);
    font-size: 12px;
    color: rgb(255, 255, 255) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-in-range::before, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-in-range::before, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-in-range::before {
    background: rgba(22, 155, 250, 0.1);
}

.search-border .left .search-list[data-v-66f82878] .ant-input .ant-picker-cell-in-range .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-picker-range .ant-picker-cell-in-range .ant-picker-cell-inner, .search-border .left .search-list[data-v-66f82878] .ant-select .ant-picker-cell-in-range .ant-picker-cell-inner {
    font-size: 12px;
    color: rgb(13, 12, 18);
}

.search-border .left .search-list[data-v-66f82878] .ant-picker-cell-today .ant-picker-cell-inner {
    color: rgb(22, 155, 250) !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-picker-cell-today .ant-picker-cell-inner::before {
    border: none !important;
}

.search-border .left .search-list[data-v-66f82878] .ant-picker-focused {
    box-shadow: none;
    border: 1px solid rgb(22, 155, 250);
}

.search-border .right[data-v-66f82878] {
    position: relative;
}

.search-border .right .line[data-v-66f82878] {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    width: 0px;
    height: 84px;
    border-left: 1px solid rgb(229, 229, 229);
}

.search-border .right img[data-v-66f82878] {
    margin-right: 8px;
}

.ant-picker-dropdown .ant-picker-cell-today .ant-picker-cell-inner {
    color: rgb(22, 155, 250) !important;
}

.ant-picker-dropdown .ant-picker-cell-today .ant-picker-cell-inner::before {
    border: none !important;
}

.ant-picker-dropdown .ant-picker-cell-in-range::before {
    background: rgba(22, 155, 250, 0.1) !important;
}

.ant-picker-dropdown .ant-picker-cell-in-range .ant-picker-cell-inner {
    font-size: 12px !important;
    color: rgb(13, 12, 18) !important;
}

.ant-picker-dropdown .ant-picker-cell-range-end .ant-picker-cell-inner, .ant-picker-dropdown .ant-picker-cell-range-start .ant-picker-cell-inner {
    background: rgb(22, 155, 250);
    font-size: 12px;
    color: rgb(255, 255, 255);
    border-radius: 50% !important;
}

.ant-picker-dropdown .ant-picker-cell-in-range::before, .ant-picker-dropdown .ant-picker-cell-range-end::before, .ant-picker-dropdown .ant-picker-cell-range-start::before {
    background: rgba(22, 155, 250, 0.1);
}

.ant-picker-dropdown .ant-picker-cell-in-range .ant-picker-cell-inner {
    font-size: 12px;
    color: rgb(13, 12, 18);
}

.ant-picker-dropdown .ant-picker-time-panel-cell-inner {
    font-size: 12px !important;
    color: rgb(13, 12, 18) !important;
}

.ant-picker-dropdown .ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    color: rgb(13, 12, 18) !important;
    background: rgba(22, 155, 250, 0.1) !important;
}

.ant-select-dropdown {
    padding: 0px !important;
    border-radius: 0px !important;
}

.ant-select-dropdown .ant-select-item {
    height: 42px !important;
    padding: 10px 12px !important;
    border-radius: 0px !important;
}

.ant-select-dropdown .ant-select-item .ant-select-item-option-content {
    font-size: 16px;
    color: rgb(56, 56, 56);
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected {
    background-color: rgb(233, 246, 254);
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected .ant-select-item-option-content {
    font-weight: 500;
    color: rgb(22, 155, 250);
}
.action-item[data-v-79bcc8ac] {
    cursor: pointer;
    color: rgb(12, 199, 219);
}

.action-item em[data-v-79bcc8ac] {
    font-size: 16px;
    margin: 0px 10px;
    color: rgb(233, 233, 233);
}

.action-item .action-label[data-v-79bcc8ac] {
    min-width: 26px;
    display: inline-block;
    text-align: center;
    font-size: 16px !important;
}

.disabled[data-v-79bcc8ac] {
    color: rgb(153, 153, 153);
    cursor: context-menu;
}
.table-wrap[data-v-af30d3dc] {
    height: fit-content;
    max-height: 100%;
    overflow: hidden;
}

.table-wrap[data-v-af30d3dc] .ant-table-placeholder {
    border: none;
}

.table-wrap[data-v-af30d3dc] .ant-checkbox-checked .ant-checkbox-inner {
    background: rgb(22, 93, 255) !important;
    border-color: transparent !important;
}

.table-wrap[data-v-af30d3dc] .ant-checkbox-checked .ant-checkbox-inner::after {
    border-width: 0px 2px 2px 0px;
    border-right-style: solid;
    border-bottom-style: solid;
    border-right-color: rgb(255, 255, 255);
    border-bottom-color: rgb(255, 255, 255);
    border-image: initial;
    border-top-style: initial;
    border-top-color: initial;
    border-left-style: initial;
    border-left-color: initial;
}

.table-wrap .table[data-v-af30d3dc] {
    height: fit-content;
    border: 1px solid rgb(226, 226, 226);
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading, .table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container {
    height: 100%;
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container .ant-table:not(.ant-table-empty) {
    background: transparent;
    height: 100%;
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container .ant-table:not(.ant-table-empty) .ant-table-content {
    height: 100%;
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container .ant-table:not(.ant-table-empty) .ant-table-content .ant-table-scroll {
    height: 100%;
    overflow: hidden;
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container .ant-table:not(.ant-table-empty) .ant-table-content .ant-table-scroll .ant-table-body {
    background: transparent;
    height: 100%;
}

.table-wrap .table[data-v-af30d3dc] .ant-spin-nested-loading .ant-spin-container .ant-table-default {
    background-color: transparent;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-thead tr th {
    position: relative;
    background-color: rgb(242, 242, 242);
    padding: 12px 16px !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-thead tr th.ant-table-cell-fix-right {
    clip-path: polygon(0px 0px, 0px 100%, calc(100% + 4px) 100%, calc(100% + 4px) 0px);
    overflow: visible;
    right: var(--table-track-size) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-thead tr th.ant-table-cell-fix-right::before {
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    transform: translateX(100%);
    width: var(--table-track-size);
    height: 100%;
    background-color: rgb(12, 75, 148);
}

.table-wrap .table[data-v-af30d3dc] .ant-table-thead tr th .ant-table-header-column {
    vertical-align: middle;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body {
    overflow-y: auto !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
    border-radius: 3px !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body::-webkit-scrollbar-track {
    border-radius: 3px !important;
    background-color: transparent !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body::-webkit-scrollbar-thumb {
    border-radius: 3px !important;
    background: rgb(204, 228, 255) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
    background: rgb(245, 250, 255);
    color: rgb(13, 12, 18);
    z-index: 1;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td .ant-checkbox-inner {
    border: 1px solid rgb(50, 116, 249) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody > tr.ant-table-row-selected td {
    background-color: unset;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-thead tr th {
    height: 40px;
    background: rgb(12, 75, 148);
    border: 0px !important;
    padding: 7px 16px !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-thead tr th .ant-checkbox-inner {
    background: transparent;
    border: 1px solid grey !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody {
    overflow: auto;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr {
    background: rgb(255, 255, 255);
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr .ant-table-cell-fix-right {
    z-index: 100 !important;
    background: rgb(255, 255, 255) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr td {
    font-size: 15px;
    padding: 16px;
    height: 48px;
    color: rgb(13, 12, 18);
    border-width: 0px 0px 1px !important;
    border-top-style: initial !important;
    border-right-style: initial !important;
    border-left-style: initial !important;
    border-top-color: initial !important;
    border-right-color: initial !important;
    border-left-color: initial !important;
    border-image: initial !important;
    border-bottom-style: solid !important;
    border-bottom-color: rgb(226, 226, 226) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr td .favorite-wrap .iconfont {
    color: rgb(255, 255, 255) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr td .ant-checkbox-inner {
    background: transparent;
    border: 1px solid grey;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr:nth-child(2n+1) {
    background: rgb(248, 248, 248);
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr:nth-child(2n+1) .ant-table-cell-fix-right {
    z-index: 100 !important;
    background: rgb(248, 248, 248) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-body .ant-table-tbody tr:last-child td {
    border-bottom: none !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-placeholder {
    background: unset;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-placeholder .ant-empty-normal .ant-empty-description {
    color: rgb(255, 255, 255);
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-body {
    overflow-y: auto !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-body::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
    border-radius: 3px !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-body::-webkit-scrollbar-track {
    border-radius: 3px !important;
    background-color: transparent !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-body::-webkit-scrollbar-thumb {
    border-radius: 3px !important;
    background: rgb(204, 228, 255) !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-body.hidden-scrollbar::-webkit-scrollbar {
    width: 0px !important;
    height: 0px !important;
    border-radius: 0px !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-header {
    background: rgb(0, 89, 130) !important;
    overflow-y: hidden !important;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-header table .ant-table-thead tr th {
    background: rgb(12, 75, 148);
    border: unset;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-content .ant-table-scroll .ant-table-header table .ant-table-thead tr th .ant-checkbox-inner {
    background: transparent;
    border: 1px solid grey;
}

.table-wrap .table[data-v-af30d3dc] .ant-table-selection .ant-checkbox-inner {
    border: 1px solid grey;
}

.table-wrap .pagination-act[data-v-af30d3dc] {
    position: relative;
    height: 32px;
}

.table-wrap .pagination-act[data-v-af30d3dc] ::-webkit-scrollbar {
    height: 0px;
    width: 0px;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] {
    color: rgb(255, 255, 255);
    font-family: "Microsoft YaHei";
    white-space: nowrap;
    overflow: auto hidden;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item {
    width: 32px;
    height: 32px;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(235, 235, 235);
    border-radius: 4px;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item a {
    font-size: 12px;
    color: rgb(56, 56, 56);
    border-radius: 4px;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-link {
    color: rgb(191, 191, 191);
    background: rgb(255, 255, 255);
    border: 1px solid rgb(235, 235, 235);
    border-radius: 4px;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-options-quick-jumper {
    font-size: 12px;
    color: rgb(56, 56, 56);
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-options-quick-jumper input {
    width: 56px;
    height: 32px;
    font-size: 12px;
    color: rgb(56, 56, 56);
    border-radius: 4px;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(235, 235, 235);
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-options .ant-select {
    position: absolute;
    left: 0px;
    top: 0px;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-0, .table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-active {
    background: rgb(50, 116, 249);
    border-color: transparent;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-0 a, .table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-active a {
    color: rgb(255, 255, 255);
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-total-text {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    color: rgb(255, 255, 255);
    margin-right: 1.25rem;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-item-ellipsis {
    color: rgb(56, 56, 56);
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-jump-next .ant-pagination-item-link {
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    background-image: none;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-jump-next .ant-pagination-item-link .ant-pagination-item-container {
    width: 100%;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-pagination-total-text {
    font-size: 12px !important;
    color: rgb(56, 56, 56) !important;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-select .ant-select-selector:focus, .table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-select .ant-select-selector:hover {
    border: 1px solid rgb(223, 225, 231) !important;
    box-shadow: none !important;
}

.table-wrap .pagination-act .ant-pagination[data-v-af30d3dc] .ant-select .ant-select-selection-item {
    font-size: 12px;
    color: rgb(56, 56, 56);
}

.ant-select-dropdown {
    z-index: 1000;
    padding: 0px !important;
}

.ant-select-dropdown, .ant-select-dropdown .ant-select-item {
    border-radius: 0px !important;
}

.ant-select-dropdown .ant-select-item .ant-select-item-option-content {
    font-size: 12px;
    color: rgb(56, 56, 56);
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected {
    background-color: rgb(233, 246, 254);
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected .ant-select-item-option-content {
    font-weight: 500;
}
body,html {
    width: 100%;
    height: 100%
}

input::-ms-clear,input::-ms-reveal {
    display: none
}

*,:after,:before {
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

body {
    margin: 0
}

[tabindex="-1"]:focus {
    outline: none
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

h1,h2,h3,h4,h5,h6 {
    margin-top: 0;
    margin-bottom: .5em;
    font-weight: 500
}

p {
    margin-top: 0;
    margin-bottom: 1em
}

abbr[data-original-title],abbr[title] {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline;
    text-decoration: underline dotted;
    border-bottom: 0;
    cursor: help
}

address {
    margin-bottom: 1em;
    font-style: normal;
    line-height: inherit
}

input[type=number],input[type=password],input[type=text],textarea {
    -webkit-appearance: none
}

dl,ol,ul {
    margin-top: 0;
    margin-bottom: 1em
}

ol ol,ol ul,ul ol,ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 500
}

dd {
    margin-bottom: .5em;
    margin-left: 0
}

blockquote {
    margin: 0 0 1em
}

dfn {
    font-style: italic
}

b,strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

sub,sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

code,kbd,pre,samp {
    font-size: 1em;
    font-family: SFMono-Regular,Consolas,Liberation Mono,Menlo,Courier,monospace
}

pre {
    margin-top: 0;
    margin-bottom: 1em;
    overflow: auto
}

figure {
    margin: 0 0 1em
}

img {
    vertical-align: middle;
    border-style: none
}

[role=button],a,area,button,input:not([type=range]),label,select,summary,textarea {
    touch-action: manipulation
}

table {
    border-collapse: collapse
}

caption {
    padding-top: .75em;
    padding-bottom: .3em;
    text-align: left;
    caption-side: bottom
}

button,input,optgroup,select,textarea {
    margin: 0;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit
}

button,input {
    overflow: visible
}

button,select {
    text-transform: none
}

[type=reset],[type=submit],button,html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

input[type=checkbox],input[type=radio] {
    box-sizing: border-box;
    padding: 0
}

input[type=date],input[type=datetime-local],input[type=month],input[type=time] {
    -webkit-appearance: listbox
}

textarea {
    overflow: auto;
    resize: vertical
}

fieldset {
    min-width: 0;
    margin: 0;
    padding: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    max-width: 100%;
    margin-bottom: .5em;
    padding: 0;
    color: inherit;
    font-size: 1.5em;
    line-height: inherit;
    white-space: normal
}

progress {
    vertical-align: baseline
}

[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: none
}

[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

output {
    display: inline-block
}

summary {
    display: list-item
}

template {
    display: none
}

[hidden] {
    display: none!important
}

mark {
    padding: .2em;
    background-color: #feffe6
}
