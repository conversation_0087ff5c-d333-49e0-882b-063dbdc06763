import { message } from 'ant-design-vue'
import { table2excel } from './table2Excel'
import { computed } from 'vue'

let locales = computed(() => JSON.parse(sessionStorage.getItem("locales")) || {})

// 处理导出操作
export function execExport(source, handler, config) {
    let { columns, dataSource } = source // 原始数据
    let { filter, calc } = handler // filter:筛选条件 calc:数据处理
    let { size, excelName } = config // excel文件配置 size:导出excel条数 excelName：导出excel文件名
    let filterColumns = filter ? columns.filter(f => filter?.indexOf(f.dataIndex) > -1) : columns;
    let excelTitle = filterColumns.map(m => m.title) // 导出excel表头
    let excelDataSource = dataSource.slice(0, size || 500)?.map((m) => {
        let item = {}
        filterColumns.forEach((n) => {
            if (calc) {
                Object.assign(item, calc(m, n)) // 自定义数据处理
            } else {
                item[n.dataIndex] = m[n.dataIndex]
            }
        })
        return Object.values(item)
    })
    // 导出excel
    table2excel([[excelTitle, ...excelDataSource], [], excelName])
    message.info(locales.value.daochuzhong)
}