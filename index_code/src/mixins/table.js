import { reactive } from 'vue'

export default function tableMixin() {
	let tableData = reactive({
		page: 1,
		pageSize: 10,
		total: 0,
		columns: [],
		dataSource: [],
		selectedRowKeys: [],
		isLoading: true,
	})
	// 表格复选
	function handleRowCheckboxChange({ tableKey, selectedRowKeys }) {
		if (tableKey) {
			tableData[tableKey].selectedRowKeys = selectedRowKeys
		} else {
			tableData.selectedRowKeys = selectedRowKeys
		}
		tableData.selectedRowKeys = tableData.selectedRowKeys.sort()
	}
	// 分页切换
	function handlePageChange({ page, pageSize, refreshTableData }) {
		tableData.page = page
		tableData.pageSize = pageSize
		refreshTableData()
	}


	return {
		tableData,
		handlePageChange,
		handleRowCheckboxChange,
	}
}
