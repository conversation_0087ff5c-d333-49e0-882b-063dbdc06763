/* eslint-disable */
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'
import moment from 'moment'
class Workbook {
	SheetNames = []
	Sheets = {}
	constructor() {

	}
}
export function table2excel(data) {
	var oo = data
	// console.log('oo', oo);
	var ranges = oo[1];

	var name = oo[2];
	const timeStamp = new Date().getTime();
	name = `${oo[2] || 'file'}${timeStamp}`

	/* original data */
	var data = oo[0];
	var ws_name = "SheetJS";

	var wb = new Workbook()
	var ws = data2sheet(data);
	// var ws = sheet_from_array_of_arrays(data);

	/* add ranges to worksheet */
	// ws['!cols'] = ['apple', 'banan'];
	ws['!merges'] = ranges;

	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	// console.log(`wb`, wb);
	var wbout = XLSX.write(wb, {
		bookType: 'xlsx',
		bookSST: false,
		type: 'binary'
	});

	saveAs(new Blob([s2ab(wbout)], {
		type: "application/octet-stream"
	}), `${name}.xlsx`)
}
export function data2sheet(data) {
	let ws = {};
	// excel范围
	let range = {
		s: { c: 10000000, r: 10000000 },
		e: { c: 0, r: 0 }
	};
	for (let R = 0; R != data.length; ++R) {
		for (let C = 0; C != data[R].length; ++C) {
			if (range.s.r > R) range.s.r = R;
			if (range.s.c > C) range.s.c = C;
			if (range.e.r < R) range.e.r = R;
			if (range.e.c < C) range.e.c = C;
			// 单元格内容
			let cell = { v: data[R][C] };
			// console.log('===cell===', cell);
			if (cell.v == null) continue;
			// 单元格在excel对应的行列
			let cell_ref = XLSX.utils.encode_cell({ c: C, r: R });
			// console.log('===cell_ref===', cell_ref);
			// 根据单元格内容编码
			if (typeof cell.v === 'number') cell.t = 'n';
			else if (typeof cell.v === 'boolean') cell.t = 'b';
			else if (cell.v instanceof Date) {
				cell.t = 'n';
				cell.z = XLSX.SSF._table[14];
				cell.v = datenum(cell.v);
			} else cell.t = 's';

			ws[cell_ref] = cell;
			// console.log('===ws===', ws);
		}
	}
	// 根据实际数据创建excel内容范围
	// console.log(XLSX.utils.encode_range(range))
	if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
	return ws;
}
// 单元格年份计算
function datenum(v, date1904) {
	if (date1904) v += 1462;
	var epoch = Date.parse(v);
	return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}
//
function s2ab(s) {
	var buf = new ArrayBuffer(s.length);
	var view = new Uint8Array(buf);
	for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
	return buf;
}
