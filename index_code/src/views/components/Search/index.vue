<template>
  <div class="search-border w100 flex-column">
    <div class="title">{{ locales.chaxuntia<PERSON>jian }}</div>
    <div class="w100 flex1 flex-row-center-between scroll">
      <div
        class="left flex1 scroll"
        :style="`max-width: calc(100% - ${
          searchDataCopy.length > 4 ? '116px' : '200px'
        });`"
      >
        <template v-for="(item, index) in searchDataCopy" :key="index">
          <!-- 输入框 -->
          <div v-if="item.type === 'input'" class="search-list">
            <div class="label">
              {{ item.label }}
            </div>
            <a-input
              v-model:value="searchDataCopy[index].value"
              :placeholder="item.placeholder || locales.qingshuru + item.label"
            />
          </div>
          <!-- 下拉框 -->
          <div v-else-if="item.type === 'select'" class="search-list">
            <div class="label">
              {{ item.label }}
            </div>
            <a-select
              :placeholder="locales.qingxuanze"
              v-model:value="searchDataCopy[index].value"
            >
              <a-select-option
                v-for="i in item.options"
                :value="i[customid]"
                :key="i"
                >{{ i[customtxt] }}
              </a-select-option>
            </a-select>
          </div>
          <!-- 级联选择框 -->
          <div v-else-if="item.type === 'cascader'" class="search-list">
            <div class="label">
              {{ item.label }}
            </div>
            <a-cascader
              v-model:value="searchDataCopy[index].value"
              :options="options"
              :load-data="loadData"
              :placeholder="locales.qingxuanze"
              change-on-select
            />
          </div>
          <!-- 时间范围 -->
          <div v-else-if="item.type === 'rangeTime'" class="search-list">
            <div class="label">
              {{ item.label }}
            </div>
            <a-range-picker
              v-model:value="searchDataCopy[index].value"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              :show-time="{
                defaultValue: [
                  moment('00:00:00', 'HH:mm:ss'),
                  moment('23:59:59', 'HH:mm:ss'),
                ],
              }"
              :placeholder="[locales.kaishishijian, locales.jieshushijian]"
            ></a-range-picker>
          </div>
        </template>
      </div>
      <div
        :class="
          searchDataCopy.length > 4
            ? 'right h100 flex-column-end-between ml16'
            : 'right h100 flex-row-center-end'
        "
      >
        <div class="line" v-if="searchDataCopy.length > 4"></div>
        <button
          class="btn ml16 flex-row-center-center"
          @click="handleEvent('search')"
        >
          <img src="@/assets/images/search.svg" alt="" />
          <span>{{ locales.chaxun }}</span>
        </button>
        <button
          class="btn btn-reset ml16 flex-row-center-center"
          @click="handleEvent('reset')"
        >
          <img src="@/assets/images/reset.svg" alt="" />
          <span>{{ locales.chongzhi }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref, watch, defineComponent } from "vue";
import moment from "moment";
import { getAllAreaData } from "@/api/list";
import { colors } from "@/assets/styles/colors.js";

export default defineComponent({
  name: "Search",
  props: {
    searchData: {
      type: Array,
      default: () => [],
    },
    disabled: {
      //是否禁用
      type: Boolean,
      default: false,
    },
    customid: {
      //select自定义value
      type: String,
      default: "value",
    },
    customtxt: {
      //select自定义labal
      type: String,
      default: "label",
    },
  },
  emits: ["handleEvent"],
  setup(props, { emit }) {
    let locales = computed(
      () => JSON.parse(sessionStorage.getItem("locales")) || {}
    );

    let showBtnArr = computed(() => props.btn.split(","));
    let searchDataCopy = ref([]);
    let options = ref([]);

    let loadData = async (selectedOptions) => {
      let targetOptions = selectedOptions[selectedOptions.length - 1];
      let { level, value } = targetOptions;
      if (level < 4)
        targetOptions.children = await getAreaData(
          level + 1,
          value,
          targetOptions
        );
      options.value = [...options.value];
    };

    watch(
      () => props.searchData,
      async (v) => {
        if (
          JSON.parse(JSON.stringify(v)) !==
          JSON.parse(JSON.stringify(searchDataCopy.value))
        )
          searchDataCopy.value = JSON.parse(JSON.stringify(v));

        let i = v.findIndex((e) => e.type == "cascader");
        if (i > -1 && !options.value.length)
          options.value = await getAreaData(2);
      },
      { deep: true, immediate: true }
    );

    function handleEvent(key) {
      let data = {};
      searchDataCopy.value = searchDataCopy.value.map((e) => {
        if (key.indexOf("reset") > -1) {
          let value =
            e.type == "number"
              ? 1000
              : e.type == "select"
              ? undefined
              : e.type === "cascader"
              ? []
              : e.type == "rangeTime"
              ? ["", ""]
              : "";
          data[e.key] = value;
          Object.assign(e, { value });
        } else data[e.key] = e.value;
        return e;
      });
      emit("handleEvent", { key, data });
    }

    async function getAreaData(level, pid, cb) {
      let res = await getAllAreaData({
        level,
        pid: pid || undefined,
      });
      if (res?.data) {
        let { code, reModel } = res.data;
        if (code === 0) {
          if (!reModel?.length) cb.isLeaf = true;
          return (
            reModel?.map((e) => ({
              label: e.name,
              value: e.id,
              level,
              isLeaf: level < 4 ? false : true,
            })) || []
          );
        }
      } else return [];
    }
    return {
      moment,
      options,
      locales,
      loadData,
      showBtnArr,
      searchDataCopy,
      handleEvent,
      colors,
    };
  },
});
</script>

<style lang="less" scoped>
.search-border {
  flex-shrink: 0;
  padding-bottom: 24px;
  // margin-bottom: 12px;
  border-bottom: 1px solid #e5e5e5;
  overflow: hidden;
  .title {
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    color: #1d2129;
    margin-bottom: 20px;
  }
  .left {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: 28px;
    grid-row-gap: 20px;
    height: fit-content;
    .search-list {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .label {
        flex-shrink: 0;
        width: fit-content;
        margin-right: 16px;
        font-size: 14px;
        color: #0d0c12;
        text-align: right;
        white-space: nowrap;
      }
      :deep(.ant-input),
      :deep(.ant-select),
      :deep(.ant-picker-range) {
        flex-shrink: 0;
        width: 256px !important;
        height: 32px !important;
        border: none;
        background: #f7f8fa;
        font-size: 14px;
        color: #86909c;
        &::placeholder {
          font-size: 14px;
          color: #86909c;
        }
        &:focus {
          box-shadow: none;
          border: 1px solid #169bfa;
        }
        .ant-select-selector {
          border: none;
          background: #f7f8fa;
          font-size: 14px;
          color: #86909c;
          .ant-select-selection-placeholder {
            font-size: 14px;
            color: #86909c;
          }
        }
        &.ant-select-focused {
          .ant-select-selector {
            box-shadow: none !important;
            border: 1px solid #169bfa !important;
          }
        }
        .ant-select-arrow {
          color: #4e5969 !important;
        }
        .ant-picker-input {
          input {
            font-size: 14px !important;
            color: #86909c !important;
            &::placeholder {
              font-size: 14px !important;
              color: #86909c !important;
            }
          }
        }
        .ant-picker-suffix {
          font-size: 12px !important;
          color: #4e5969 !important;
        }
        .ant-picker-active-bar {
          display: none;
        }
        .ant-picker-cell-disabled {
          font-size: 12px;
          color: #bfbfbf;
          &::before {
            background-color: transparent;
          }
          &.ant-picker-cell-in-view {
            color: #0d0c12;
          }
        }
        .ant-picker-cell-range-start,
        .ant-picker-cell-range-end {
          .ant-picker-cell-inner {
            background: rgba(22, 155, 250, 0.1);
            font-size: 12px;
            color: #fff !important;
          }
        }
        .ant-picker-cell-in-range {
          &::before {
            background: rgba(22, 155, 250, 0.1);
          }
          .ant-picker-cell-inner {
            font-size: 12px;
            color: #0d0c12;
          }
        }
      }
      :deep(.ant-picker-cell-today) {
        .ant-picker-cell-inner {
          color: #169bfa !important;
          &::before {
            border: none !important;
          }
        }
      }
      :deep(.ant-picker-focused) {
        box-shadow: none;
        border: 1px solid #169bfa;
      }
    }
  }

  .right {
    position: relative;
    .line {
      position: absolute;
      left: 0%;
      top: 50%;
      transform: translateY(-50%);
      width: 0px;
      height: 84px;
      border-left: 1px solid #e5e5e5;
    }
    img {
      margin-right: 8px;
    }

    .btn:not(.btn-reset) {
      background: v-bind('colors.gradientBlue') !important;

      &:hover {
        background: v-bind('colors.gradientBlueEnd') !important;
      }
    }
  }
}
</style>
<style lang="less">
.ant-picker-dropdown {
  .ant-picker-cell-today {
    .ant-picker-cell-inner {
      color: #169bfa !important;
      &::before {
        border: none !important;
      }
    }
  }
  .ant-picker-cell-in-range {
    &::before {
      background: rgba(22, 155, 250, 0.1) !important;
    }
    .ant-picker-cell-inner {
      font-size: 12px !important;
      color: #0d0c12 !important;
    }
  }
  .ant-picker-cell-range-start,
  .ant-picker-cell-range-end {
    .ant-picker-cell-inner {
      background: #169bfa;
      font-size: 12px;
      color: #fff;
      border-radius: 50% !important;
    }
    &::before {
      background: rgba(22, 155, 250, 0.1);
    }
  }
  .ant-picker-cell-in-range {
    &::before {
      background: rgba(22, 155, 250, 0.1);
    }
    .ant-picker-cell-inner {
      font-size: 12px;
      color: #0d0c12;
    }
  }
  .ant-picker-time-panel-cell-inner {
    font-size: 12px !important;
    color: #0d0c12 !important;
  }
  .ant-picker-time-panel-cell-selected {
    .ant-picker-time-panel-cell-inner {
      color: #0d0c12 !important;
      background: rgba(22, 155, 250, 0.1) !important;
    }
  }
}
.ant-select-dropdown {
  padding: 0 !important;
  border-radius: 0 !important;
  .ant-select-item {
    height: 42px !important;
    padding: 10px 12px !important;
    border-radius: 0 !important;
    .ant-select-item-option-content {
      font-size: 16px;
      color: #383838;
    }
    &.ant-select-item-option-selected {
      background-color: #e9f6fe;
      .ant-select-item-option-content {
        font-weight: 500;
        color: #169bfa;
      }
    }
  }
}
</style>

