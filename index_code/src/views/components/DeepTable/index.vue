<template>
  <div class="deep-table w100">
    <Search
      v-if="searchData?.length"
      class="mb16"
      :searchData="searchData"
      @handleEvent="handleTableFilter"
    />
    <!-- tabs -->
    <a-tabs v-model:activeKey="activeKey" type="card" v-if="tabs?.length">
      <a-tab-pane
        :key="tab.value"
        :tab="tab.label"
        v-for="tab in tabs"
      ></a-tab-pane>
    </a-tabs>
    <!-- btns -->
    <div class="btns flex-row-center-end w100 mb16" v-if="btns?.length">
      <button
        class="btn ml16"
        v-for="item in btns"
        :key="item.value"
        @click="() => handleBtnClick(item)"
      >
        {{ item.label }}
      </button>
    </div>
    <Table
      v-if="columns?.length"
      :style="`height: calc(100% - ${searchData?.length ? 121 : 0}px - ${
        btns?.length ? 48 : 0
      }px - ${tabs?.length ? 56 : 0}px)`"
      :refreshTableData="
        searchData?.length ? () => handleQuery(queryData) : handleQuery
      "
      :rowKey="rowKey"
      :hasSelection="hasSelection"
      :isLoading="tableData.isLoading"
      :columns="tableData.columns"
      :dataSource="tableData.dataSource"
      :page="tableData.page"
      :pageSize="tableData.pageSize"
      :total="tableData.total"
      :selectedRowKeys="tableData.selectedRowKeys"
      @pageChange="handlePageChange"
      @emitRowCheckboxChange="handleRowCheckboxChange"
    >
    </Table>
  </div>
</template>

<script setup>
import { watch, ref, defineAsyncComponent } from "vue";
import tableMixin from "@/mixins/table.js";
import { message } from "ant-design-vue";
const Search = defineAsyncComponent(() =>
  import("@/views/components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("@/views/components/Table/index.vue")
);

let props = defineProps({
  searchData: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  dataSource: {
    type: Array,
    default: () => [],
  },
  api: {
    type: Function,
    default: () => () => {},
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  tabs: {
    type: Array,
    default: () => [],
  },
  btns: {
    type: Array,
    default: () => [],
  },
  refresh: {
    type: Boolean,
    default: false,
  },
  hasSelection: {
    type: Boolean,
    default: true,
  },
  rowKey: { type: String, default: "id" },
});
let emits = defineEmits(["handleTabChange", "handleBtnClick"]);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();

let activeKey = ref(1);
let filter = ref([]);
let queryData = ref(null);

async function handleTableFilter(v) {
  queryData.value = { ...props.params, ...v.data };
  tableData.page = 1;
  handleQuery(queryData.value);
}
const dataList = ref([]);
async function handleQuery(data) {
  let res = await props.api(
    data
      ? {
          pageNo: tableData.page,
          pageSize: tableData.pageSize,
          ...(data || {}),
        }
      : {
          pageNo: tableData.page,
          pageSize: tableData.pageSize,
          ...props.params,
        }
  );
  tableData.isLoading = false;
  tableData.dataSource = [];
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      dataList.value = JSON.parse(JSON.stringify(reModel.data));
      tableData.dataSource = filter.value.length
        ? reModel.data.map((e) => {
            filter.value.forEach((v) => {
              e[v.key] =
                v.options.find((o) => o.value == e[v.key])?.label || "";
            });
            return e;
          })
        : reModel.data;
    }
  }
}

function handleBtnClick(item) {
  if (item.value == "back")
    emits("handleBtnClick", { type: item.value, value: [] });
  else {
    if (
      !props.hasSelection ||
      (props.hasSelection && tableData.selectedRowKeys.length)
    )
      emits("handleBtnClick", {
        type: item.value,
        value: tableData.selectedRowKeys,
        items:
          dataList.value.filter(
            (e) =>
              tableData.selectedRowKeys.findIndex((v) => v == e[props.rowKey]) >
              -1
          ) || [],
      });
    else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}

watch(
  () => props.columns,
  (v) => {
    if (v?.length) {
      tableData.columns = JSON.parse(JSON.stringify(v));
      filter.value = v.filter((e) => e.options?.length) || [];
      handleQuery();
    } else {
      filter.value = [];
      tableData.columns = [];
      tableData.dataSource = [];
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => activeKey.value,
  (v) => {
    emits("handleTabChange", v);
  },
  { deep: true }
);
watch(
  () => props.refresh,
  (v) => {
    if (v) handleQuery();
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.deep-table {
  height: calc(100% - 54px);
  padding: 16px 24px;
  background-color: #fff;
}
</style>