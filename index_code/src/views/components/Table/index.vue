<template>
  <div ref="wrapperRef" class="table-wrap flex-column w100">
    <a-table
      ref="antTable"
      class="table scroll"
      :rowKey="rowKey"
      :bordered="bordered"
      :pagination="false"
      :loading="isLoading"
      :rowSelection="rowSelection"
      :dataSource="dataSource"
      :showHeader="showHeader"
      :customRow="handleCustomRowClick"
    >
      <!-- :align="column.align || 'left'"  -->
      <a-table-column
        v-for="column in columnsMap"
        :key="column.dataIndex"
        :align="column.align || 'left'"
        :dataIndex="column.dataIndex"
        :width="column.width"
        :ellipsis="true"
        :fixed="column.fixed"
      >
        <!-- 表格 thead title -->
        <template #title>
          <ColumnTitle
            :column="column"
            :title="column.title"
            :tips="column.tips"
          ></ColumnTitle>
        </template>
        <!-- 表格内容 -->
        <template #default="{ text, record }">
          <!-- 如果是自定义内容 -->
          <template v-if="column.type && column.type == 'slot'">
            <slot
              class="flex-row-center-start"
              :name="column.slotName || column.dataIndex"
              :text="text"
              :record="record"
              :column="column"
            ></slot>
          </template>
          <template v-else-if="column.type == 'action'">
            <CellAction
              @handleRowClick="handleRowClick"
              :cellData="text"
              :column="column"
              :record="record"
            >
            </CellAction>
          </template>
          <template v-else-if="column.type == 'status'">
            <CellStatus :cellData="text"></CellStatus>
          </template>
          <template v-else>
            <CellText :cellData="text" :column="column"></CellText>
          </template>
        </template>
      </a-table-column>
    </a-table>
    <div
      ref="paginationRef"
      v-if="paginationFlag"
      class="mt16 flex-row-center-end pagination-act"
    >
      <a-pagination
        showLessItems
        v-model:current="page"
        show-size-changer
        show-quick-jumper
        :page-size="pageSize"
        :pageSizeOptions="pageSizeOptions"
        :total="total"
        :show-total="(total) => `${locales.gong} ${total} ${locales.tiao}`"
        @change="pageChange"
        :locale="{
          items_per_page: locales.itemsPerPage,
          jump_to: locales.jumpTo,
          page: locales.page,
        }"
      />
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  defineAsyncComponent,
  computed,
  ref,
  reactive,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick,
} from "vue";

import throttle from "lodash/throttle";
const ColumnTitle = defineAsyncComponent(() =>
  import("./modules/ColumnTitle.vue")
);
const CellAction = defineAsyncComponent(() =>
  import("./modules/CellAction.vue")
);
const CellText = defineAsyncComponent(() => import("./modules/CellText.vue"));
const CellStatus = defineAsyncComponent(() =>
  import("./modules/CellStatus.vue")
);
export default defineComponent({
  name: "Table",
  components: {
    ColumnTitle,
    CellText,
    CellAction,
    CellStatus,
  },
  props: {
    tableKey: { type: String, default: "" },
    rowKey: { type: String, default: "id" }, //指定选中该字段值
    bordered: { type: Boolean, default: false }, // 表格是否带线
    isLoading: { type: Boolean, default: true }, //表格重载loading
    scroll: {
      type: Object,
      default: () => {
        return { x: 0, y: 0 };
      },
    }, //表格超出滚动
    hasIndex: { type: Boolean, default: false }, //是否带序列号
    hasSelection: { type: Boolean, default: true }, // 是否需要多行选择
    columns: { type: Array, default: () => [] }, //表头
    dataSource: { type: Array, default: () => [] }, //表格数据
    paginationFlag: { type: Boolean, default: true }, //是否带分页
    page: { type: Number, default: 1 }, //页数
    pageSize: { type: Number, default: 10 }, //单页展示数据数
    pageSizeOptions: {
      type: Array,
      default: () => ["10", "20", "30", "50"],
    }, //每页条数配置
    total: { type: Number, default: 0 }, // 数据总数
    noActionFlag: { type: Boolean, default: false }, // 单表格无操作表头
    resizeHeightOffset: { type: Number, default: 150 },
    selectedRowKeys: { type: Array, default: () => [] },
    refreshTableData: {
      type: Function,
      default: () => () => {},
    }, // 刷新列表数据
    isRowSelection: { type: Boolean, default: true },
    width: { type: Number, default: 0 },
    size: { type: String, default: "" }, //翻页size
    checkType: { type: String, default: "multi" },
    model: { type: String, default: "simple" },
    offsetHeight: { type: Number, default: 0 },
    showHeader: { type: Boolean, default: true },
    keyIndexWidth: { type: [Number, String], default: 60 },
    virtualScroll: { type: Boolean, default: false },
    autoScroll: { type: Boolean, default: false },
    hideScrollbar: { type: Boolean, default: false },
  },
  emits: [
    "emitRowCheckboxChange",
    "handleRowClick",
    "pageChange",
    "handleWholeRowClick",
  ],
  setup(props, { emit, expose }) {
    // 吸底设置内行高
    let contentHeight = ref(0);
    let sizePage = false;
    let page = ref(1);
    //   配置序号列
    let dataSourceMap = computed(() => {
      return (props.dataSource || []).map((e, i) =>
        Object.assign(e, { key: i + 1 })
      );
    });
    let locales = computed(
      () => JSON.parse(sessionStorage.getItem("locales")) || {}
    );

    watch(
      () => dataSourceMap.value.length,
      (v) => {
        nextTick(() => {
          // getFixedOffset();
          if (!props.isLoading && wrapperRef.value) {
            if (v) {
              if (!innerEl)
                nextTick(() => {
                  initVirtualScroll();
                });
              else wrapResize();
            } else removeVirtualScroll();
          }
          if (props.autoScroll) autoScroll();
        });
      },
      { deep: true }
    );
    watch(
      () => props.page,
      (v) => {
        page.value = v;
      },
      { deep: true }
    );
    // 配置复选
    let rowSelection = props.isRowSelection
      ? computed(() => {
          return props.hasSelection
            ? {
                selectedRowKeys:
                  props.checkType == "single"
                    ? props.selectedRowKeys[1]
                      ? [props.selectedRowKeys[1]]
                      : props.selectedRowKeys[0]
                      ? [props.selectedRowKeys[0]]
                      : []
                    : props.selectedRowKeys,
                onChange: (selectedRowKeys) =>
                  handleRowCheckboxChange(
                    props.checkType == "single"
                      ? selectedRowKeys[1]
                        ? [selectedRowKeys[1]]
                        : selectedRowKeys[0]
                        ? [selectedRowKeys[0]]
                        : []
                      : selectedRowKeys
                  ),
                columnWidth: 50,
              }
            : null;
        })
      : null;
    let mixScroll = computed(() => {
      return {
        x:
          props.scroll.x ||
          (props.model != "simple"
            ? props.columns.length * 120 + props.keyIndexWidth
            : 0),
        y: props.scroll.y || contentHeight.value,
      };
    });
    // 翻页
    function pageChange(page, pageSize) {
      if (props.isLoading) {
        message.warning("表格内容正在加载，请勿频繁操作!");
        return;
      }
      emit("pageChange", {
        page,
        pageSize,
        refreshTableData: props.refreshTableData,
      });
    }

    //   添加序号列
    let columnsMap = computed(() => {
      let map = props.columns
        .filter((e) => !e.extend)
        .map((k) => ({
          width: props.model == "simple" ? null : 120,
          ...k,
        }));
      let item = {
        dataIndex: "key",
        title: "序号",
        width: props.keyIndexWidth,
        align: "left",
        fixed: null,
      };
      // 如果有fixed: left，序号也需要fixed
      if (props.columns.some((e) => e.fixed && e.fixed === "left")) {
        item.fixed = "left";
      }
      return props.hasIndex ? [item, ...map] : map;
    });
    // 表格复选
    function handleRowCheckboxChange(selectedRowKeys) {
      emit("emitRowCheckboxChange", {
        tableKey: props.tableKey,
        selectedRowKeys,
      });
    }
    // 表格整行点击
    function handleCustomRowClick(e) {
      return {
        onClick: () => {
          emit("handleWholeRowClick", e);
        },
      };
    }
    //筛选展开
    function handleRowClick(clickType, rowData, record, e) {
      emit("handleRowClick", clickType, rowData, record, e);
    }

    const wrapperRef = ref(null);
    const paginationRef = ref();
    function getFixedOffset() {
      if (wrapperRef.value) {
        const UA = navigator.userAgent;
        const tableDom =
          wrapperRef.value.getElementsByClassName("ant-table-body")[0];
        if (tableDom.scrollHeight > tableDom.clientHeight && /chrome/i.test(UA))
          wrapperRef.value.style.setProperty("--table-track-size", "4px");
        else wrapperRef.value.style.setProperty("--table-track-size", "0px");
      }
    }
    function getRemSize() {
      const screenWidth = window.innerWidth;
      if (screenWidth < 1280) return 12;
      else if (screenWidth < 1680) return 13;
      else if (screenWidth < 1980) return 14;
      else return 15;
    }
    let rowHeight = 32;
    let scrollEl = null;
    let wrapEl = null;
    let innerEl = null;
    function resetSize() {
      if (sizePage) return;
      sizePage = true;
      const headerHeight = getRemSize() * 1.5715 + 18;
      contentHeight.value =
        wrapperRef.value?.clientHeight -
        (props.paginationFlag
          ? paginationRef.value.clientHeight + getRemSize() * 1.25
          : 0) -
        (props.showHeader ? headerHeight : 0) -
        props.offsetHeight;
      sizePage = false;
    }
    function wrapResize() {
      if (wrapEl)
        wrapEl.style.height = dataSourceMap.value.length * rowHeight + "px";
    }
    let hoverFlag = false;
    function autoScroll() {
      if (!hoverFlag && innerEl) {
        resetLen();
        scrollEl.scrollTop = 0;
        handleScroll({ target: scrollEl });
      }
    }
    function scrollHover() {
      hoverFlag = true;
    }
    function scrollLeave() {
      hoverFlag = false;
    }
    function handleScroll(e) {
      renderSize.start = Math.floor(e.target.scrollTop / rowHeight) - 10;
      innerEl.style.transform = `translateY(${
        Math.max(renderSize.start, 0) * rowHeight
      }px)`;
    }
    const onScroll = throttle(handleScroll, 10);
    const renderSize = reactive({ start: 0, len: 20 });
    const renderData = computed(() =>
      dataSourceMap.value.slice(
        Math.max(renderSize.start, 0),
        renderSize.start + renderSize.len
      )
    );
    function setVirtualScroll() {
      if (!wrapEl) {
        scrollEl = wrapperRef.value.querySelector(".ant-table-body");
        if (props.hideScrollbar)
          scrollEl.className = (scrollEl.className ?? "") + " hidden-scrollbar";
        scrollEl.addEventListener("scroll", onScroll);
        scrollEl.addEventListener("mouseover", scrollHover);
        scrollEl.addEventListener("mouseleave", scrollLeave);
        wrapEl = document.createElement("div");
        innerEl = document.createElement("div");
        wrapEl.style.display = "inline-block";
        wrapEl.style.width = "100%";
        innerEl.style.display = "inline-block";
        innerEl.style.width = "100%";
        wrapResize();
        innerEl.appendChild(scrollEl.children[0]);
        wrapEl.appendChild(innerEl);
        scrollEl.insertBefore(wrapEl, scrollEl.firstChild);
        scrollEl.wrapEl = wrapEl;
      }
    }
    function resetLen() {
      renderSize.len = Math.ceil(mixScroll.value.y / rowHeight) + 20;
    }
    function initVirtualScroll() {
      if (props.virtualScroll) {
        rowHeight = wrapperRef.value
          .querySelector(".ant-table-row")
          ?.getBoundingClientRect()?.height;
        setVirtualScroll();
        wrapResize();
        resetLen();
        scrollEl.scrollTop = 0;
        handleScroll({ target: scrollEl });
      }
    }
    function removeVirtualScroll() {
      if (wrapEl && innerEl) {
        scrollEl.removeEventListener("scroll", onScroll);
        scrollEl.removeEventListener("mouseover", scrollHover);
        scrollEl.removeEventListener("mouseleave", scrollLeave);
        scrollEl.insertBefore(innerEl.children[0], scrollEl.firsetChild);
        innerEl.remove();
        innerEl = null;
        wrapEl.remove();
        wrapEl = null;
      }
    }
    watch(
      () => props.isLoading,
      (v) => {
        if (!v && wrapperRef.value) {
          if (dataSourceMap.value.length)
            nextTick(() => {
              initVirtualScroll();
            });
          else removeVirtualScroll();
        }
      }
    );
    function pageResize() {
      nextTick(() => {
        rowHeight = wrapperRef.value
          .querySelector(".ant-table-row")
          ?.getBoundingClientRect()?.height;
        wrapResize();
        resetSize();
        resetLen();
      });
    }
    onMounted(() => {
      window.addEventListener("resize", pageResize);
    });
    onBeforeUnmount(() => {
      window.removeEventListener("resize", pageResize);
    });
    expose({
      resize: resetSize,
      resetLen,
    });

    return {
      rowSelection,
      page,
      pageChange,
      locales,
      columnsMap,
      dataSourceMap,
      renderData,
      contentHeight,
      mixScroll,
      sizePage,
      handleRowCheckboxChange,
      handleCustomRowClick,
      wrapperRef,
      paginationRef,
      handleRowClick,
    };
  },
});
</script>
<style lang="less" scoped>
.table-wrap {
  height: fit-content;
  max-height: 100%;
  overflow: hidden;
  :deep(.ant-table-placeholder) {
    border: none;
  }

  :deep(.ant-checkbox-checked) {
    .ant-checkbox-inner {
      background: #165dff !important;
      border-color: transparent !important;

      &::after {
        border: 2px solid #fff;
        border-top: 0;
        border-left: 0;
      }
    }
  }

  .table {
    height: fit-content;
    border: 1px solid #e2e2e2;

    :deep(.ant-spin-nested-loading) {
      height: 100%;

      .ant-spin-container {
        height: 100%;

        .ant-table:not(.ant-table-empty) {
          background: transparent;
          height: 100%;

          .ant-table-content {
            height: 100%;

            .ant-table-scroll {
              height: 100%;
              overflow: hidden;

              .ant-table-body {
                background: transparent;
                height: 100%;
              }
            }
          }
        }

        .ant-table-default {
          background-color: transparent;
        }
      }
    }

    :deep(.ant-table-thead) {
      tr {
        th {
          padding: 12px 16px !important;
          position: relative;
          background-color: #f2f2f2;

          // 解决chrome上因为tbody有滚动条导致的thead右侧不对齐
          &.ant-table-cell-fix-right {
            right: var(--table-track-size) !important;
            clip-path: polygon(
              0 0,
              0 100%,
              calc(100% + 4px) 100%,
              calc(100% + 4px) 0
            );
            overflow: visible;

            &:before {
              content: "";
              position: absolute;
              top: 0;
              right: 0;
              transform: translateX(100%);
              width: var(--table-track-size);
              height: 100%;
              background-color: #0c4b94;
            }
          }

          .ant-table-header-column {
            vertical-align: middle;
          }
        }
      }
    }

    :deep(.ant-table-body) {
      overflow-y: auto !important;

      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
        border-radius: 3px !important;
      }

      &::-webkit-scrollbar-track {
        border-radius: 3px !important;
        background-color: transparent !important;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 3px !important;
        background: #cce4ff !important;
      }
      .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
        background: #f5faff;
        color: #0d0c12;
        z-index: 1;
        .ant-checkbox-inner {
          border: 1px solid #3274f9 !important;
        }
      }

      .ant-table-tbody > tr.ant-table-row-selected td {
        background-color: unset;
      }

      .ant-table-thead {
        tr {
          th {
            height: 40px;
            background: #0c4b94;
            border: 0 !important;
            padding: 7px 16px !important;

            .ant-checkbox-inner {
              background: transparent;
              border: 1px solid #808080 !important;
            }
          }
        }
      }

      .ant-table-tbody {
        overflow: auto;

        tr {
          background: #fff;

          .ant-table-cell-fix-right {
            z-index: 100 !important;
            background: #fff !important;
          }

          td {
            font-size: 15px;
            padding: 16px 16px;
            height: 48px;
            color: #0d0c12;
            border: 0 !important;
            border-bottom: 1px solid #e2e2e2 !important;
            .favorite-wrap {
              .iconfont {
                color: #fff !important;
              }
            }

            .ant-checkbox-inner {
              background: transparent;
              border: 1px solid #808080;
            }
          }

          &:nth-child(odd) {
            background: #f8f8f8;

            .ant-table-cell-fix-right {
              z-index: 100 !important;
              background: #f8f8f8 !important;
            }
          }
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }
        }
      }
    }

    :deep(.ant-table-placeholder) {
      background: unset;

      .ant-empty-normal {
        .ant-empty-description {
          color: #fff;
        }
      }
    }

    :deep(.ant-table-content) {
      .ant-table-scroll {
        .ant-table-body {
          overflow-y: auto !important;

          &::-webkit-scrollbar {
            width: 6px !important;
            height: 6px !important;
            border-radius: 3px !important;
          }

          &::-webkit-scrollbar-track {
            border-radius: 3px !important;
            background-color: transparent !important;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 3px !important;
            background: #cce4ff !important;
          }

          &.hidden-scrollbar {
            &::-webkit-scrollbar {
              width: 0 !important;
              height: 0 !important;
              border-radius: 0 !important;
            }
          }
        }

        .ant-table-header {
          background: #005982 !important;
          overflow-y: hidden !important;

          table {
            .ant-table-thead {
              tr {
                th {
                  background: #0c4b94;
                  border: unset;

                  .ant-checkbox-inner {
                    background: transparent;
                    border: 1px solid #808080;
                  }
                }
              }
            }
          }
        }
      }
    }
    :deep(.ant-table-selection) {
      .ant-checkbox-inner {
        border: 1px solid #808080;
      }
    }
  }

  .pagination-act {
    position: relative;
    height: 32px;

    ::-webkit-scrollbar {
      height: 0;
      width: 0;
    }

    .ant-pagination {
      color: #fff;
      font-family: Microsoft YaHei;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;

      :deep(.ant-pagination-item) {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #ebebeb;
        border-radius: 4px;

        a {
          font-size: 12px;
          color: #383838;
          border-radius: 4px;
        }
      }

      :deep(.ant-pagination-item-link) {
        color: #bfbfbf;
        background: #fff;
        border: 1px solid #ebebeb;
        border-radius: 4px;
      }

      :deep(.ant-pagination-options-quick-jumper) {
        font-size: 12px;
        color: #383838;

        input {
          width: 56px;
          height: 32px;
          font-size: 12px;
          color: #383838;
          border-radius: 4px;
          background: #fff;
          border: 1px solid #ebebeb;
        }
      }
      :deep(.ant-pagination-options) {
        .ant-select {
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      :deep(.ant-pagination-item-0),
      :deep(.ant-pagination-item-active) {
        background: rgba(150, 150, 150, 1);
        border-color: transparent;

        a {
          color: #fff;
        }
      }

      :deep(.ant-pagination-total-text) {
        display: inline-flex;
        align-items: center;
        font-size: 0.875rem;
        color: #fff;
        margin-right: 1.25rem;
      }

      :deep(.ant-pagination-item-ellipsis) {
        color: #383838;
      }

      :deep(.ant-pagination-jump-next) {
        .ant-pagination-item-link {
          display: flex;
          justify-content: center;
          align-items: center;
          border: none;
          background-image: none;

          .ant-pagination-item-container {
            width: 100%;
          }
        }
      }

      :deep(.ant-pagination-total-text) {
        font-size: 12px !important;
        color: #383838 !important;
      }
      :deep(.ant-select) {
        .ant-select-selector {
          &:focus,
          &:hover {
            border: 1px solid #dfe1e7 !important;
            box-shadow: none !important;
          }
        }
        .ant-select-selection-item {
          font-size: 12px;
          color: #383838;
        }
      }
    }
  }
}
</style>
<style lang="less">
.ant-select-dropdown {
  z-index: 1000;
  padding: 0 !important;
  border-radius: 0 !important;
  .ant-select-item {
    border-radius: 0 !important;
    .ant-select-item-option-content {
      font-size: 12px;
      color: #383838;
    }
    &.ant-select-item-option-selected {
      background-color: #e9f6fe;
      .ant-select-item-option-content {
        font-weight: 500;
      }
    }
  }
}
</style>
