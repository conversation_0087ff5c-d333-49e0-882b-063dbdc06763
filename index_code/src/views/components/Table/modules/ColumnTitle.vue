<template>
  <div class="column-title">
    <span>{{ title }}</span>
    <a-tooltip v-if="tips">
      <template #title>{{ tips }}</template>
      <QuestionCircleOutlined class="ml4" />
    </a-tooltip>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'ColumnTitle',
  components: {
    QuestionCircleOutlined
  },
  props: {
    column: { type: Object, default: () => ({}) },
    title: { type: String, default: '' },
    tips: { type: String, default: '' }
  }
});
</script>

<style scoped>
.column-title {
  display: flex;
  align-items: center;
}
.ml4 {
  margin-left: 4px;
}
</style>
