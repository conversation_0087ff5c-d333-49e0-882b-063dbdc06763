<template>
  <div class="text-con table-cell ellipsis">
    <!-- 有悬浮弹层 -->
    <a-tooltip v-if="column.tooltip" placement="topLeft">
      <template #title>
        <i v-if="!!(cellData instanceof Array)">
          <i v-for="(ee, i) in cellData" :key="i">
            {{ trim(ee.label)
            }}{{
              i == cellData.length - 1
                ? ""
                : column.splitType || defaultSplitType
            }}
          </i>
        </i>
        <i v-else-if="!!(cellData instanceof Object)">{{
          trim(cellData?.label)
        }}</i>
        <i v-else>{{ trim(cellData) }}</i>
      </template>
      <i v-if="!!(cellData instanceof Array)">
        <i v-for="(ee, i) in cellData" :key="i">
          {{ trim(ee.label)
          }}{{
            i == cellData.length - 1 ? "" : column.splitType || defaultSplitType
          }}
        </i>
      </i>
      <i v-else-if="!!(cellData instanceof Object)">{{
        trim(cellData?.label)
      }}</i>
      <i v-else>{{ trim(cellData) }}</i>
    </a-tooltip>
    <!-- 无悬浮弹层 -->
    <div class="ellipsis" v-else>
      <i v-if="!!(cellData instanceof Array)">
        <i v-for="(ee, i) in cellData" :key="i">
          {{ trim(ee.label)
          }}{{
            i == cellData.length - 1 ? "" : column.splitType || defaultSplitType
          }}
        </i>
      </i>
      <i v-else-if="!!(cellData instanceof Object)">{{
        trim(cellData?.label)
      }}</i>
      <i v-else>{{ trim(cellData) }}</i>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
let props = defineProps({
  column: {
    type: Object,
    default: () => {},
  },
  cellData: {
    type: [Object, Number, String, Array, Boolean],
    default: () => {},
  },
});
let defaultText = ref("");
let defaultSplitType = "，";
// 数据处理
function trim(d) {
  return (typeof d == "string" ? d.trim() : d) || defaultText.value;
}
</script>

<style lang="less" scoped>
.action-item {
  cursor: pointer;
  color: #0cc7db;

  em {
    font-size: 16px;
    margin: 0 10px;
    color: #e9e9e9;
  }

  .action-label {
    font-size: 16px !important;
    min-width: 26px;
    display: inline-block;
    text-align: center;
  }
}

.disabled {
  color: #999;
  cursor: context-menu;
}
</style>
