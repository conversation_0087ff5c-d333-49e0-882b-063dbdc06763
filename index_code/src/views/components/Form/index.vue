<template>
  <div class="form-list w100 scroll">
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item
        :label="item.label"
        :name="item.key"
        :rules="
          item.required
            ? [
                {
                  required: true,
                  message: item.label + locales.bunengweikong,
                  trigger: 'blur',
                },
              ]
            : []
        "
        v-for="(item, index) in searchDataCopy"
        :key="index"
        :class="!isEdit ? '' : item.disabled && !item.slot ? 'label-dis' : ''"
      >
        <!-- 输入框 -->
        <a-input
          v-if="item.type == 'input'"
          v-model:value="searchDataCopy[index].value"
          :placeholder="
            !isEdit ? '' : item.placeholder || locales.qingshuru + item.label
          "
          :disabled="item.disabled || !isEdit"
        />
        <!-- 数字输入框 -->
        <a-input-number
          v-if="item.type == 'input-number'"
          v-model:value="searchDataCopy[index].value"
          :placeholder="
            !isEdit ? '' : item.placeholder || locales.qingshuru + item.label
          "
          :disabled="item.disabled || !isEdit"
          :min="1"
          @change="
            (value) =>
              Number.isInteger(value) && value > 0
                ? ''
                : (searchDataCopy[index].value = Math.round(value))
          "
        ></a-input-number>
        <!-- 查询输入框 -->
        <a-input-search
          v-if="item.type == 'input-search'"
          v-model:value="searchDataCopy[index].value"
          :enter-button="locales.chaxun"
          :placeholder="!isEdit ? '' : locales.qingshuru + item.label"
          :disabled="item.disabled || !isEdit"
          @search="(point) => getLocationByAddress(point)"
        />
        <!-- 富文本输入框 -->
        <a-textarea
          v-if="item.type == 'textarea'"
          class="textarea"
          v-model:value="searchDataCopy[index].value"
          :placeholder="!isEdit ? '' : locales.qingshuru + item.label"
          :disabled="item.disabled || !isEdit"
        />
        <!-- 下拉框 -->
        <a-select
          v-else-if="item.type === 'select'"
          v-model:value="searchDataCopy[index].value"
          :options="item.options"
          :placeholder="!isEdit ? '' : locales.qingxuanze"
          :disabled="item.disabled || !isEdit"
        >
        </a-select>
        <!-- 级联选择框 -->
        <a-select
          v-else-if="item.type === 'cascader'"
          v-model:value="searchDataCopy[index].value"
          :options="casOptions[item.level - 1]"
          :placeholder="!isEdit ? '' : locales.qingxuanze"
          :disabled="item.disabled || !isEdit"
        ></a-select>
        <!-- 时间选择器 -->
        <a-time-picker
          v-else-if="item.type === 'time'"
          v-model:value="searchDataCopy[index].value"
          valueFormat="HH:mm"
          format="HH:mm"
          :locale="zhCN"
          :placeholder="!isEdit ? '' : item.placeholder || ''"
          :disabled="item.disabled || !isEdit"
        />
        <!-- 日期选择器 -->
        <a-date-picker
          v-else-if="item.type === 'date'"
          show-time
          v-model:value="searchDataCopy[index].value"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          :placeholder="
            !isEdit ? '' : item.placeholder || locales.qingxuanze + item.label
          "
        />
        <!-- 文件上传 -->
        <input
          type="file"
          @change="(file) => emit('handleFileUpload', file)"
          v-else-if="item.type === 'upload'"
        />
        <!-- 插槽Slot -->
        <template v-if="item.slot && isEdit">
          <slot :name="item.key"></slot>
        </template>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
        <button class="btn mr16" v-if="isEdit" @click="handleEvent('save')">
          {{ locales.save }}
        </button>
        <button class="btn" @click="handleEvent('back')">
          {{ locales.fanhui }}
        </button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { getAllAreaData } from "@/api/list";
import { message } from "ant-design-vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
let props = defineProps({
  formData: {
    type: Array,
    default: () => [],
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});
let emit = defineEmits(["handleEvent", "handleFileUpload"]);

let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

let searchDataCopy = ref([]);
let casOptions = ref([]);
let formRef = ref(null);
let formState = computed(() => {
  let items = {};
  searchDataCopy.value.forEach((e) => {
    items[e.key] = e.value;
  });
  return items;
});

watch(
  () => props.formData,
  async (v) => {
    if (
      JSON.parse(JSON.stringify(v)) !==
      JSON.parse(JSON.stringify(searchDataCopy.value))
    )
      searchDataCopy.value = JSON.parse(JSON.stringify(v));
  },
  { deep: true, immediate: true }
);

let preVal1 = undefined;
let preVal2 = undefined;

watch(
  () => searchDataCopy.value,
  async (v) => {
    let i = v.findIndex((e) => e.type == "cascader" && e.level == 1); // 国家
    let m = v.findIndex((e) => e.type == "cascader" && e.level == 2); // 省地区
    let n = v.findIndex((e) => e.type == "cascader" && e.level == 3); // 城市

    if (i > -1) {
      // 如果没有获取过国家opts
      if (!casOptions.value.length) casOptions.value[0] = await getAreaData(2);
      // 国家切换
      if (preVal1 != v[i].value) {
        // 初始化省地区和城市
        if (preVal1) {
          if (m > -1) searchDataCopy.value[m].value = undefined;
          if (n > -1) searchDataCopy.value[n].value = undefined;
        }
        // 加载省地区
        casOptions.value[1] = await getAreaData(3, v[i].value);
        // 加载城市
        if (v[m].value) casOptions.value[2] = await getAreaData(4, v[m].value);
        // 更新preVal1
        preVal1 = v[i].value;
      } else {
        // 省地区切换
        if (m > -1 && preVal2 != v[m].value) {
          // 加载城市
          casOptions.value[2] = await getAreaData(4, v[m].value);
          // 初始化城市
          if (preVal2) {
            if (n > -1) searchDataCopy.value[n].value = undefined;
          }
        }
      }
    }
  },
  { deep: true, immediate: true }
);

async function getAreaData(level, pid) {
  let res = await getAllAreaData({
    level,
    pid: pid || undefined,
  });
  if (res?.data) {
    let { code, reModel } = res.data;
    if (code === 0) {
      return (
        reModel?.map((e) => ({
          label: e.name,
          value: e.id,
        })) || []
      );
    }
  } else return [];
}
function handleEvent(key) {
  let data = {};
  if (key == "save") {
    formRef.value
      .validateFields()
      .then(() => {
        // 校验成功
        for (const e of searchDataCopy.value) {
          data[e.key] = e.value;
        }
        emit("handleEvent", { key, data });
      })
      .catch((errors) => {
        // 校验失败，可以根据需要处理错误信息。
        // console.log("表单校验失败:", errors);
        return;
      });
  } else {
    emit("handleEvent", { key, data });
  }
}

// 定义一个函数来获取地址的经纬度
function getLocationByAddress(address) {
  var myGeo = new BMap.Geocoder(); // 创建地理编码实例
  myGeo.getPoint(
    address,
    function (point) {
      if (point) {
        // 获取到经纬度后执行的代码
        let i = searchDataCopy.value.findIndex((e) => e.key == "latitude");
        let j = searchDataCopy.value.findIndex((e) => e.key == "longitude");
        if (i > -1) searchDataCopy.value[i].value = point.lat;
        if (j > -1) searchDataCopy.value[j].value = point.lng;
      } else {
        message.info("未找到");
      }
    },
    "全国"
  ); // 指定城市，如果不指定则默认全国范围搜索
}
</script>

<style lang="less" scoped>
.form-list {
  height: calc(100% - 54px);
  padding: 30px 24px;
  background-color: #fff;
  .ant-form {
    width: 500px;
    :deep(.ant-input),
    :deep(.ant-input-number),
    :deep(.ant-select),
    :deep(.ant-picker),
    :deep(.ant-input-search) {
      width: 100%;
      height: 32px !important;
      border: none;
      background: #f7f8fa;
      font-size: 14px;
      color: #86909c;
      border-radius: 2px;
      overflow: hidden;
      &.textarea {
        height: 82px !important;
      }
      &::placeholder {
        font-size: 14px;
        color: #86909c;
      }
      &:focus {
        box-shadow: none;
        border: 1px solid #169bfa;
      }
      .ant-select-selector {
        border: none;
        background: #f7f8fa;
        font-size: 14px;
        color: #86909c;
        .ant-select-selection-placeholder {
          font-size: 14px;
          color: #86909c;
        }
      }
      &.ant-select-focused {
        .ant-select-selector {
          box-shadow: none !important;
          border: 1px solid #169bfa !important;
        }
      }
      .ant-select-arrow {
        color: #4e5969 !important;
      }
      .ant-picker-input {
        input {
          font-size: 14px !important;
          color: #86909c !important;
          &::placeholder {
            font-size: 14px !important;
            color: #86909c !important;
          }
        }
        .ant-picker-suffix,
        .ant-picker-clear {
          display: none !important;
        }
      }
      .ant-picker-suffix {
        font-size: 12px !important;
        color: #4e5969 !important;
      }
      .ant-picker-active-bar {
        display: none;
      }
      .ant-picker-cell-disabled {
        font-size: 12px;
        color: #bfbfbf;
        &::before {
          background-color: transparent;
        }
        &.ant-picker-cell-in-view {
          color: #0d0c12;
        }
      }
      .ant-picker-cell-range-start,
      .ant-picker-cell-range-end {
        .ant-picker-cell-inner {
          background: rgba(22, 155, 250, 0.1);
          font-size: 12px;
          color: #fff !important;
        }
      }
      .ant-picker-cell-in-range {
        &::before {
          background: rgba(22, 155, 250, 0.1);
        }
        .ant-picker-cell-inner {
          font-size: 12px;
          color: #0d0c12;
        }
      }
      .ant-input {
        border: none;
        background: #f7f8fa;
        font-size: 14px;
        color: #86909c;
        &::placeholder {
          font-size: 14px;
          color: #86909c;
        }
        &:focus {
          box-shadow: none;
          border: 1px solid #169bfa;
        }
      }
    }
    :deep(.ant-picker-cell-today) {
      .ant-picker-cell-inner {
        color: #169bfa !important;
        &::before {
          border: none !important;
        }
      }
    }
    :deep(.ant-picker-focused) {
      box-shadow: none;
      border: 1px solid #169bfa;
    }
    :deep(.ant-input-number-focused) {
      box-shadow: none;
      border: 1px solid #169bfa;
    }
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: nowrap;
    }
    :deep(.ant-input) {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .label-dis {
      opacity: 0.5 !important;
    }
  }
}
</style>

<style lang="less">
.ant-picker-dropdown {
  .ant-picker-cell-today {
    .ant-picker-cell-inner {
      color: #169bfa !important;
      &::before {
        border: none !important;
      }
    }
  }
  .ant-picker-cell-in-range {
    &::before {
      background: rgba(22, 155, 250, 0.1) !important;
    }
    .ant-picker-cell-inner {
      font-size: 12px !important;
      color: #0d0c12 !important;
    }
  }
  .ant-picker-cell-range-start,
  .ant-picker-cell-range-end {
    .ant-picker-cell-inner {
      background: #169bfa;
      font-size: 12px;
      color: #fff;
      border-radius: 50% !important;
    }
    &::before {
      background: rgba(22, 155, 250, 0.1);
    }
  }
  .ant-picker-cell-in-range {
    &::before {
      background: rgba(22, 155, 250, 0.1);
    }
    .ant-picker-cell-inner {
      font-size: 12px;
      color: #0d0c12;
    }
  }
  .ant-picker-time-panel-cell-inner {
    font-size: 12px !important;
    color: #0d0c12 !important;
  }
  .ant-picker-time-panel-cell-selected {
    .ant-picker-time-panel-cell-inner {
      color: #0d0c12 !important;
      background: rgba(22, 155, 250, 0.1) !important;
    }
  }
}
.ant-select-dropdown {
  padding: 0 !important;
  border-radius: 0 !important;
  .ant-select-item {
    height: 42px !important;
    padding: 10px 12px !important;
    border-radius: 0 !important;
    .ant-select-item-option-content {
      font-size: 16px;
      color: #383838;
    }
    &.ant-select-item-option-selected {
      background-color: #e9f6fe;
      .ant-select-item-option-content {
        font-weight: 500;
        color: #169bfa;
      }
    }
  }
}
</style>