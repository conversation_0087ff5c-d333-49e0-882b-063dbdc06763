<template>
  <div class="station-list wh100 plr16">
    <a-breadcrumb class="p16">
      <img
          src="@/assets/images/red_line.png"
          alt=""
          style="width: 5px; height: 18px; margin-right: 8px"
        />
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.shebeiguanli
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">

      <!-- 搜索条件区域 -->
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
       <!-- 操作按钮区域 -->
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div class="btn-special" @click="() => handleEvent('select-cloud')">
            <span>{{ locales.xuanzecaijiqi }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('select-inverter')"
          >
            <span>{{ locales.xuanzenibianqi }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('debug-switch')"
          >
            <span>{{ locales.tiaoshikaiguan }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('view-device')"
          >
            <span>{{ locales.chakanshebei }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('select-owners')"
          >
            <span>{{ locales.xuanzeyezhu }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzeng }}
          </button>
        </div>
      </div>
       <!-- 表格区域 -->
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #status="{ text }">
          <span :class="`status-btn-${text} flex-row-center-center`">
            {{ statusOpt.find((e) => e.value == text)?.label || "" }}
          </span>
        </template>
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :tabs="tabs"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      :searchData="searchDeepData"
      @handleTabChange="handleTabChange"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const {
  stationColumn3,
  selectCloudColumn,
  selectInverterColumn,
  selectComponentColumn,
  selectOwnerColumn,
} = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deletePowerStation,
  selectCloudTerminal,
  selectInverter,
  getOwnerList,
  selectOwner,
  selectComponent,
  selectEquipment,
  stationDeleteEquipment,
  debugSwitch,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";
import { colors } from "@/assets/styles/colors.js";
const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

let flag = ref({});

onBeforeMount(() => {
  tableData.columns = stationColumn3;
  handleQuery();
});

const typeOpt = [
  { label: "V08", value: 1 },
  { label: "PLC", value: 2 },
];
const layoutTypeOpt = [
  { label: "组件模式", value: 1 },
  { label: "组串模式", value: 2 },
];
const statusOpt = [
  { label: "待调试", value: 1 },
  { label: "已调试", value: 2 },
];
const gradeOpt = [
  { label: "微型", value: 1 },
  { label: "小型", value: 2 },
  { label: "中型", value: 3 },
  { label: "大型", value: 4 },
  { label: "特大型", value: 5 },
];
const searchData = ref([
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.chaungjianshijian,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
  {
    label: "编号",
    key: "systemNo",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.status,
    key: "status",
    value: undefined,
    type: "select",
    options: statusOpt,
    width: 30,
  },
  {
    label: "电站位置",
    key: "area",
    value: [],
    type: "cascader",
  },
  {
    label: locales.value.dianzhanjibie,
    key: "grade",
    value: undefined,
    type: "select",
    options: gradeOpt,
  },
  {
    label: "详细地址",
    key: "streetName",
    value: undefined,
    type: "input",
    width: 30,
  },
]);
let formData = ref([
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.dianzhangonglv,
    key: "power",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.dianzhanleixing,
    key: "type",
    value: undefined,
    type: "select",
    options: typeOpt,
    required: true,
  },
  {
    label: locales.value.bujumoshi,
    key: "layoutType",
    value: undefined,
    type: "select",
    options: layoutTypeOpt,
    required: true,
  },
  {
    label: locales.value.guojia,
    key: "countriesId",
    value: undefined,
    type: "cascader",
    level: 1,
  },
  {
    label: locales.value.shengdiqu,
    key: "province",
    value: undefined,
    type: "cascader",
    level: 2,
  },
  {
    label: locales.value.chengshi,
    key: "cityId",
    value: undefined,
    type: "cascader",
    level: 3,
  },
  {
    label: "详细地址",
    key: "streetName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xuanzejingweidu,
    key: "coordinate",
    value: undefined,
    type: "input-search",
  },
  {
    label: locales.value.jingdu,
    key: "longitude",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.weidu,
    key: "latitude",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.richushijian,
    key: "sunuptime",
    value: undefined,
    type: "time",
    placeholder: locales.value.richushijiangeshi,
    required: true,
  },
  {
    label: locales.value.riluoshijian,
    key: "sundowntime",
    value: undefined,
    type: "time",
    placeholder: locales.value.riluoshijiangeshi,
    required: true,
  },
  {
    label: locales.value.caijishijianjiange,
    key: "collectGap",
    value: undefined,
    type: "input",
    required: true,
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("powerstation/queryPowerStationList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    systemName: data?.systemName || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
    systemNo: data?.systemNo || "",
    createUserName: data?.createUserName || "",
    countriesId: data?.area[0] || "",
    province: data?.area[1] || "",
    cityId: data?.area[2] || "",
    streetName: data?.streetName || "",
    status: data?.status || "",
    grade: data?.grade || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data.map((e) => {
        return {
          ...e,
          grade: gradeOpt.find((v) => v.value == e.grade)?.label || "",
          createTimeCh: e.createTimeCh ? new Date(e.createTimeCh).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '/') : "",
          location: [e.countries, e.provinceName, e.cityName].filter(Boolean).join('-') || "",
        };
      });
    }
  }
}
async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    let { createUserName, coordinate, sunuptime, sundowntime, ...others } =
      data;
    others.sunuptimech = sunuptime;
    others.sundowntimech = sundowntime;
    if (flag.value.value == "update") others.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("powerstation", others);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

let viewData = ref([]);
let tabs = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let searchDeepData = ref([]);
let btns = ref([]);

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}

async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type != "view-device") tabs.value = [];
  if (type != "select-owners") searchDeepData.value = [];

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      if (v.key != "createUserName" && v.key != "coordinate") {
        if (["countriesId", "province", "cityId"].indexOf(v.key) > -1) {
          v.value = Number(curData[v.key]) || undefined;
        } else v.value = curData[v.key];
      }
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value
        .filter((e) => e.key != "coordinate")
        .map((v) => {
          if (["countriesId", "province", "cityId"].indexOf(v.key) > -1) {
            v.value = Number(curData[v.key]) || undefined;
          } else v.value = curData[v.key];
          let { required, ...others } = v;
          return {
            ...others,
            disabled: true,
          };
        }) || [];

    let { createTimeCh, cloudTerminalNum, componentNum, inverterNum } = curData;
    let arr = [
      {
        label: locales.value.chaungjianshijian,
        key: "createTimeCh",
        value: createTimeCh,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.yunzhongduan,
        key: "cloudTerminalNum",
        value: cloudTerminalNum,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.nibianqi,
        key: "inverterNum",
        value: inverterNum,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.zujian,
        key: "componentNum",
        value: componentNum,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deletePowerStation({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "select-cloud") {
    // 选择采集器
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (curData?.status != 1) return message.warning(locales.value.nocaijiqi);

    flag.value = { label: locales.value.xuanzecaijiqi, value: "deep" };
    curApi.value = selectCloudTerminal;
    params.value = {
      selectCloudTerminal: "selectCloudTerminal",
    };
    deepColumn.value = selectCloudColumn;
    btns.value = [
      { label: locales.value.queren, value: "cloud-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "select-inverter") {
    // 选择逆变器
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (curData?.status != 1) return message.warning(locales.value.nonibianqi);

    flag.value = { label: locales.value.xuanzenibianqi, value: "deep" };
    curApi.value = selectInverter;
    params.value = {
      selectInverter: "selectInverter",
    };
    deepColumn.value = selectInverterColumn;
    btns.value = [
      { label: locales.value.queren, value: "inverter-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "debug-switch") {
    // 调试开关
    flag.value = {};
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    let res = await debugSwitch({
      id: tableData.selectedRowKeys[0],
      status: curData?.status || 1,
    });
    message.info(res?.data?.rec || "");
  } else if (type == "view-device") {
    // 查看设备
    flag.value = { label: locales.value.chakanshebei, value: "deep" };
    curApi.value = selectCloudTerminal;
    params.value = {
      powerStationId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = selectCloudColumn;
    btns.value = [
      { label: locales.value.yichu, value: "remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
    tabs.value = [
      { label: locales.value.caijiqi, value: 1 },
      { label: locales.value.nibianqi, value: 2 },
      { label: locales.value.zujian, value: 3 },
    ];
  } else if (type == "select-owners") {
    // 选择业主
    flag.value = { label: locales.value.xuanzeyezhu, value: "deep" };
    curApi.value = getOwnerList;
    deepColumn.value = selectOwnerColumn;
    btns.value = [
      { label: locales.value.queren, value: "owner-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
    searchDeepData.value = [
      {
        label: "用户账号",
        key: "phone",
        value: undefined,
        type: "input",
      },
      {
        label: "真实姓名",
        key: "name",
        value: undefined,
        type: "input",
      },
    ];
  }

  if (flag.value.value != "deep") btns.value = [];
}

let equipmentType = ref(1);
function handleTabChange(tab) {
  equipmentType.value = tab;
  deepColumn.value =
    tab == 1
      ? selectCloudColumn
      : tab == 2
      ? selectInverterColumn
      : selectComponentColumn;
  params.value = {
    powerStationId: tableData.selectedRowKeys[0],
  };
  curApi.value =
    tab == 1
      ? selectCloudTerminal
      : tab == 2
      ? selectInverter
      : selectComponent;
}

let refresh = ref(false);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "cloud-confirm" || type == "inverter-confirm") {
            if (value?.length != 1)
              return message.info(locales.value.zhengquexuanzecaozuoxiang);
            res = await selectEquipment({
              id: tableData.selectedRowKeys[0],
              equipmentType: type == "inverter-confirm" ? 2 : 1, // 设备类型 1云终端 2逆变器 3组件
              equipmentId: value[0],
            });
          } else if (type == "remove") {
            res = await stationDeleteEquipment({
              id: tableData.selectedRowKeys[0],
              equipmentType: equipmentType.value, // 设备类型 1云终端 2逆变器 3组件
              equipmentId: value.join(","),
            });
          } else if (type == "owner-confirm") {
            if (value?.length != 1)
              return message.info(locales.value.zhengquexuanzecaozuoxiang);
            res = await selectOwner({
              memberId: value[0],
              powerStationId: tableData.selectedRowKeys[0],
            });
            message.info(res?.data?.message || "");
            return;
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}
</script>

<style lang="less" scoped>
.station-list {
  .btns {
    margin: 20px 0 12px;
  }
  .status-btn-1 {
    width: 56px;
    height: 24px;
    font-size: 12px;
    border-radius: 12px;
    color: v-bind('colors.statusUnprocessed');
    background: v-bind('colors.statusUnprocessedBg');
    border: 1px solid v-bind('colors.statusUnprocessedBorder');
  }
  .status-btn-2 {
    width: 56px;
    height: 24px;
    font-size: 12px;
    color: v-bind('colors.statusProcessed');
    border-radius: 12px;
    background: v-bind('colors.statusProcessedBg');
    border: 1px solid v-bind('colors.statusProcessedBorder');
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: v-bind('colors.primary');

        &:hover {
          color: v-bind('colors.primaryHover');
          background-color: v-bind('colors.primaryBackground');
        }
      }

      &.action-btn-danger {
        color: v-bind('colors.ljRed');

        &:hover {
          color: v-bind('colors.ljRedHover');
          background-color: v-bind('colors.ljRedBackground');
        }
      }
    }
  }
}
</style>

<style lang="less">
.ant-modal-confirm-content {
  font-size: 16px !important;
  color: #0d0c12 !important;
}
</style>
