<template>
  <div class="version-list wh100 plr16">
    <a-breadcrumb class="p16">
      <a-breadcrumb-item class="cursor">
        <img
          src="@/assets/images/banben.png"
          alt=""
          style="width: 18px; height: 18px"
        />
      </a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.banbenguanli
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">
      <div class="btns flex-row-center-end">
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzeng }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('view')">
            {{ locales.chakan }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="handleQuery"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      />
    </div>
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
      @handleFileUpload="handleFileUpload"
    ></Form>
  </div>
</template>

<script setup>
import { defineAsyncComponent, onBeforeMount, ref, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { versionColumn } = require("@/db.js");
import { getDataList, versionSaveOrUpdate, deleteVersion } from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let flag = ref({});
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

const boardTypeOpt = [
  { label: "采集器", value: "collector" },
  { label: "中继器", value: "repeater" },
  { label: "优化器", value: "optimizer" },
];

onBeforeMount(() => {
  tableData.columns = versionColumn;
  handleQuery();
});

async function handleQuery() {
  let res = await getDataList("version/queryVersionList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            boardType:
              boardTypeOpt.find((v) => v.value == e.boardType)?.label || "",
          };
        }) || [];
    }
  }
}

let formData = ref([
  {
    label: locales.value.shangchuanwenjian,
    key: "upload",
    value: undefined,
    type: "upload",
  },
]);
let viewData = ref([]);
async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增
    if (selectedFile.value) {
      const { name } = selectedFile.value;
      const typeList = [
        "collector-General",
        "repeater-General",
        "optimizer-General",
      ];
      if (typeList.findIndex((e) => name.indexOf(e) > -1) < 0) {
        return message.warning(locales.value.zipgeshi);
      }

      const formData = new FormData();
      formData.append("upFile", selectedFile.value);
      let res = await versionSaveOrUpdate(formData);
      if (res?.data?.rec == "FAL") message.warning(locales.value.caozuoshibai);
      else message.success(locales.value.caozuochenggong);
    } else {
      return message.warning(locales.value.qingxuanzebanbenwenjian);
    }

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

async function handleEvent(type) {
  if (type != "add") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };

    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    let {
      versionId,
      fileName,
      boardType,
      hardware,
      firmware,
      mcuSupport,
      bomSupport,
      boardId,
      versionCreateTime,
      createTime,
    } = curData;
    viewData.value = [
      {
        label: locales.value.banbenbiaoshi,
        key: "versionId",
        value: versionId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.banbenwenjian,
        key: "fileName",
        value: fileName,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.banbenleixing,
        key: "boardType",
        value: boardType,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.yingjianbanbenhao,
        key: "hardware",
        value: hardware,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.gujianbanben,
        key: "firmware",
        value: firmware,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.mcuSupport,
        key: "mcuSupport",
        value: mcuSupport,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.bomSupport,
        key: "bomSupport",
        value: bomSupport,
        type: "input",
        disabled: true,
      },
      {
        label: "boardId",
        key: "boardId",
        value: boardId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.versionCreateTime,
        key: "versionCreateTime",
        value: versionCreateTime,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.banbenshangchuanshijian,
        key: "createTime",
        value: createTime,
        type: "input",
        disabled: true,
      },
    ];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteVersion({
          id: tableData.selectedRowKeys[0],
          fileName:
            tableData.dataSource?.find(
              (e) => e.id == tableData.selectedRowKeys[0]
            )?.fileName || "",
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  }
}

let selectedFile = ref(null);
function handleFileUpload(event) {
  selectedFile.value = event.target.files[0]; // 获取第一个文件
  if (selectedFile.value) {
    const { name } = selectedFile.value;
    const typeList = [
      "collector-General",
      "repeater-General",
      "optimizer-General",
    ];
    if (typeList.findIndex((e) => name.indexOf(e) > -1) < 0) {
      return message.warning(locales.value.zipgeshi);
    }
  }
}
</script>

<style lang="less" scoped>
.version-list {
  .btns {
    margin-bottom: 12px;
  }
  :deep(.ant-form-item-control-input) {
    width: 455px !important;
  }
}
</style>