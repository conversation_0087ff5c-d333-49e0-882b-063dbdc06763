<template>
  <div class="query-task wh100 plr16">
    <a-breadcrumb class="p16">
      <a-breadcrumb-item class="cursor">
        <img
          src="@/assets/images/chaxun.png"
          alt=""
          style="width: 18px; height: 18px"
        />
      </a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.chaxunrenwu
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div class="btn-special" @click="() => handleEvent('end')">
            <span>{{ locales.jieshurenwu }}</span>
          </div>
          <div class="btn-special ml16" @click="() => handleEvent('view')">
            <span>{{ locales.chakanchaxunjieguo }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('retry')">
            {{ locales.chongshi }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('refresh')">
            {{ locales.shuaxin }}
          </button>
        </div>
      </div>
      <Table
        rowKey="taskId"
        :refreshTableData="handleQuery"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      />
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      :hasSelection="false"
      @handleBtnClick="handleBtnClick"
    />
  </div>
</template>

<script setup>
import { defineAsyncComponent, onBeforeMount, ref, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { queryTaskColumn, queryResultColumn } = require("@/db.js");
import {
  refreshQueryTask,
  retryQueryTask,
  queryTaskResult,
  endTask,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let flag = ref({});
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

const typeOpt = [
  { label: "采集器", value: 1 },
  { label: "中继器", value: 2 },
  { label: "优化器", value: 3 },
  { label: "组串", value: 4 },
];
const queryTypeOpt = [
  { label: "查询自身", value: 1 },
  { label: "查询所属组件", value: 2 },
];
const statusOpt = [
  { label: "查询中", value: -1 },
  { label: "未开始", value: 0 },
  { label: "查询结束", value: 1 },
  { label: "采集器没有响应，任务停止", value: 2 },
];

onBeforeMount(() => {
  tableData.columns = queryTaskColumn;
  handleQuery();
});

let dataList = ref([]);
async function handleQuery(condition) {
  let res = await refreshQueryTask({
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      dataList.value = reModel.data || [];
      tableData.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            type: typeOpt.find((v) => v.value == e.type)?.label || "",
            queryType:
              queryTypeOpt.find((v) => v.value == e.queryType)?.label || "",
            status: statusOpt.find((v) => v.value == e.status)?.label || "",
          };
        }) || [];
    }
    if (condition) {
      if (code === 0) message.success(locales.value.caozuochenggong);
      else message.warning(locales.value.caozuoshibai);
    }
  }
}

let btns = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let refresh = ref(false);
function handleEvent(type) {
  if (type == "refresh") {
    // 刷新
    handleQuery(true);
  } else {
    if (tableData.selectedRowKeys.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
    else {
      let curData =
        dataList.value.find((e) => e.taskId == tableData.selectedRowKeys[0]) ||
        {};
      if (type == "retry") {
        // 重试
        if (curData.status != 1) return message.info("任务没有完成，无需重试!");
        else {
          Modal.confirm({
            title: locales.value.tishi,
            content: locales.value.caozuoqueren,
            centered: true,
            onOk: async () => {
              let res = await retryQueryTask({
                taskId: tableData.selectedRowKeys[0],
              });
              if (res?.data?.code === 0) {
                message.success(locales.value.caozuochenggong);
                handleQuery();
              } else {
                message.warning(locales.value.caozuoshibai);
              }
            },
          });
        }
      } else if (type == "end") {
        // 结束任务
        if (curData.status == 1)
          return message.info("任务已结束，无需再次结束!");
        else {
          Modal.confirm({
            title: locales.value.tishi,
            content: locales.value.caozuoqueren,
            centered: true,
            onOk: async () => {
              let res = await endTask({
                taskId: tableData.selectedRowKeys[0],
              });
              if (res?.data?.code === 0) {
                message.success(locales.value.caozuochenggong);
                handleQuery();
              } else {
                message.warning(locales.value.caozuoshibai);
              }
            },
          });
        }
      } else if (type == "view") {
        // 查看查询结果
        flag.value = { label: locales.value.chakanchaxunjieguo, value: "deep" };
        curApi.value = queryTaskResult;
        let { queryType, type, queryId, imei, taskId } = curData;
        params.value = { queryType, type, queryId, imei, taskId };
        deepColumn.value = queryResultColumn;
        btns.value = [
          { label: locales.value.shuaxin, value: "confirm" },
          { label: locales.value.fanhui, value: "back" },
        ];
      }
    }
  }
}
function handleBtnClick({ type }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    refresh.value = true;
    message.success(locales.value.caozuochenggong);
  }
}
</script>

<style lang="less" scoped>
.query-task {
  .btns {
    margin-bottom: 12px;
  }
}
</style>