
<template>
  <div class="warning-list wh100 plr16">
    <a-breadcrumb class="p16">

        <img
          src="@/assets/images/red_line.png"
          alt=""
          style="width: 5px; height: 18px; margin-right: 8px"
        />

        <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
          locales.jinggoguanli
        }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
      <div class="btns flex-row-center-end">
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('export')">
            {{ locales.yijiandaochu }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        :hasSelection="false"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #warningStatus="{ text }">
          <span :class="`status-btn-${text} flex-row-center-center`">
            {{ warningStatusOpt.find((e) => e.value == text)?.label || "" }}
          </span>
        </template>
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowAction('deal', record)">
              {{ locales.chuli }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowAction('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowAction('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <Form
      v-else
      :isEdit="flag.value == 'update'"
      :formData="formData"
      @handleEvent="handleFormEvent"
    ></Form>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { warningColumn } = require("@/db.js");
import { execExport } from "@/mixins/common.js";
import {
  getDataList,
  batchUpdateWarning,
  deleteWarnings,
  saveOrUpdate,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";
import { colors } from "@/assets/styles/colors.js";

const Search = defineAsyncComponent(() =>
  import("../components/Search/flexSearch.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let flag = ref({});
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

onBeforeMount(() => {
  tableData.columns = warningColumn;
  handleQuery();
});

const warningTypeOpt = [
  { label: "温度报警", value: 1 },
  { label: "遮挡报警", value: 2 },
  { label: "超限警报", value: 3 },
  { label: "遮挡解除报警", value: 5 },
];
const warningStatusOpt = [
  { label: "未处理", value: 1 },
  { label: "已处理", value: 2 },
];
const equipmentTypeOpt = [
  { label: "优化器", value: 1 },
  { label: "逆变器 ", value: 2 },
  { label: "组件", value: 3 },
  { label: "采集器", value: 4 },
];

const searchData = ref([
  // 第一行：电站名称、警告时间、查询按钮
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
    row: 1,
  },
  {
    label: locales.value.fashengshijian,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
    row: 1,
  },
  {
    label: "",
    key: "searchBtn",
    value: undefined,
    type: "searchButton",
    row: 1,
  },
  // 第二行：警告类型、状态、重置按钮
  {
    label: locales.value.jinggaoleixing,
    key: "warningType",
    value: undefined,
    type: "select",
    options: warningTypeOpt,
    row: 2,
  },
  {
    label: locales.value.status,
    key: "warningStatus",
    value: undefined,
    type: "select",
    options: warningStatusOpt,
    row: 2,
  },
  {
    label: "",
    key: "resetBtn",
    value: undefined,
    type: "resetButton",
    row: 2,
  },
]);
let formData = ref([
  {
    label: locales.value.jinggaoleixing,
    key: "warningType",
    value: undefined,
    type: "select",
    options: warningTypeOpt,
    required: true,
  },
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
    disabled: true,
    required: true,
  },
  {
    label: locales.value.status,
    key: "warningStatus",
    value: undefined,
    type: "select",
    options: warningStatusOpt,
    required: true,
  },
  {
    label: locales.value.jinggaoneirong,
    key: "warningContent",
    value: undefined,
    type: "textarea",
    required: true,
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("warning/queryWarningListTo.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    systemName: data?.systemName || "",
    warningType: data?.warningType || "",
    warningStatus: data?.warningStatus || "",
    createTimeBegin: data?.createTime?.[0] || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    createTimeEnd: data?.createTime?.[1] || new Date().toISOString().split('T')[0],
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            warningType:
              warningTypeOpt.find((v) => v.value == e.warningType)?.label || "",
            // warningStatus:
            //   warningStatusOpt.find((v) => v.value == e.warningStatus)?.label ||
            //   "",
            equipmentType:
              equipmentTypeOpt.find((v) => v.value == e.equipmentType)?.label ||
              "",
            createTime: e.createTime ? new Date(e.createTime).toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }).replace(/\//g, '-') : "",
          };
        }) || [];
    }
  }
}

let currentEditId = ref(null);

async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 修改
    data.id = currentEditId.value;
    let res = await saveOrUpdate("warning", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    currentEditId.value = null;
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
    currentEditId.value = null;
  }
}
// 处理行操作
async function handleRowAction(type, record) {
  if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    currentEditId.value = record.id;
    formData.value.forEach((v) => {
      if (v.key == "warningType") {
        v.value =
          warningTypeOpt.find((e) => e.label == record[v.key])?.value || "";
      } else v.value = record[v.key];
    });
  } else if (type == "deal") {
    // 处理
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.caozuoqueren,
      centered: true,
      onOk: async () => {
        let res = await batchUpdateWarning({
          id: record.id,
          warningStatus: 2,
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "delete") {
    // 删除
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteWarnings({
          id: record.id,
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  }
}

async function handleEvent(type) {
  if (type == "export") {
    // 导出
    const { columns, dataSource } = tableData;
    execExport(
      { columns, dataSource },
      {},
      { excelName: "警告数据", size: dataSource.length }
    );
  }
}
</script>

<style lang="less" scoped>
.warning-list {
  .btns {
    margin: 20px 0 12px;

    .btn {
      background: v-bind('colors.gradientBlue');
      color: white;
      border: none;
      border-radius: 10px;
      padding: 8px 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;

      &:hover {
        background: v-bind('colors.gradientBlueEnd');
      }
    }
  }
  .status-btn-1 {
    width: 56px;
    height: 24px;
    font-size: 12px;
    border-radius: 12px;
    color: v-bind('colors.statusUnprocessed');
    background: v-bind('colors.statusUnprocessedBg');
    border: 1px solid v-bind('colors.statusUnprocessedBorder');
  }
  .status-btn-2 {
    width: 56px;
    height: 24px;
    font-size: 12px;
    color: v-bind('colors.statusProcessed');
    border-radius: 12px;
    background: v-bind('colors.statusProcessedBg');
    border: 1px solid v-bind('colors.statusProcessedBorder');
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: v-bind('colors.primary');

        &:hover {
          color: v-bind('colors.primaryHover');
          background-color: v-bind('colors.primaryBackground');
        }
      }

      &.action-btn-danger {
        color: v-bind('colors.ljRed');

        &:hover {
          color: v-bind('colors.ljRedHover');
          background-color: v-bind('colors.ljRedBackground');
        }
      }
    }
  }
}
</style>