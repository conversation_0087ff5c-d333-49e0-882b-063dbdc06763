<template>
  <div class="upgrade-task wh100 plr16">
    <a-breadcrumb class="p16">
      <a-breadcrumb-item class="cursor">
        <img
          src="@/assets/images/rocket.png"
          alt=""
          style="width: 18px; height: 18px"
        />
      </a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.shengjirenwu
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div class="btn-special" @click="() => handleEvent('end')">
            <span>{{ locales.jieshurenwu }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('query-version')"
          >
            <span>{{ locales.shengjibanbenchaxun }}</span>
          </div>
          <div
            class="btn-special ml16"
            @click="() => handleEvent('view-result')"
          >
            <span>{{ locales.chakanshengjijieguo }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzeng }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('update')">
            {{ locales.xiugai }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('view')">
            {{ locales.chakan }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('retry')">
            {{ locales.chongshi }}
          </button>
          <button class="btn ml16" @click="() => handleEvent('refresh')">
            {{ locales.shuaxin }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="handleQuery"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      />
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :rowKey="rowKey"
      :hasSelection="hasSelection"
      :btns="btns"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      :searchData="searchDeepData"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    >
      <template #versionId>
        <button
          class="btn ml16 nowrap"
          @click="handleComponentChoose('version')"
        >
          {{ locales.xuanzebanben }}
        </button> </template
      ><template #imei>
        <button class="btn ml16 nowrap" @click="handleComponentChoose('imei')">
          {{ locales.xuanzecaijiqi }}
        </button>
      </template>
      <template #componentId>
        <button
          :disabled="formData[2].btnDis"
          class="btn ml16 nowrap"
          @click="handleComponentChoose('component')"
        >
          {{ locales.xuanzezujian }}
        </button>
      </template>
    </Form>
  </div>
</template>

<script setup>
import { defineAsyncComponent, onBeforeMount, ref, computed, watch } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const {
  upgradeTaskColumn,
  updateResultColumn,
  updateComponentColumn,
  updateVersionColumn,
  updateImeiColumn,
} = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteUpdateTask,
  getTaskComponent,
  getTaskVersion,
  selectCloudTerminal,
  retryUpdateTask,
  endUpdateTask,
  versionQuery,
  updateTaskResult,
  getTaskDetails,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

const typeOpt = [
  { label: "采集器", value: "collector" },
  { label: "中继器", value: "repeater" },
  { label: "优化器", value: "optimizer" },
];
const taskStatusOpt = [
  { label: "版本查询中", value: -4 },
  { label: "升级信息发送中", value: -3 },
  { label: "中继加载中", value: -2 },
  { label: "版本发送中", value: -1 },
  { label: "", value: 0, updateStatus: [{ label: "未开始", value: 0 }] },
  {
    label: "已完成",
    value: 1,
    updateStatus: [
      { label: "版本发送成功,已完成", value: 1 },
      { label: "版本发送失败,已完成", value: 2 },
      { label: "中继加载失败,已完成", value: 4 },
      { label: "升级成功,已完成", value: 5 },
      { label: "升级失败,已完成", value: 6 },
      { label: "发送升级信息成功,已完成", value: 7 },
      { label: "发送升级信息失败,已完成", value: 8 },
      { label: "版本发送失败,采集器没有响应,已完成", value: 21 },
      { label: "中继加载失败,采集器没有响应,已完成", value: 41 },
      { label: "升级部分成功,已完成", value: 55 },
      { label: "升级失败,采集器没有响应,已完成", value: 61 },
      { label: "发送升级信息部分成功,已完成", value: 77 },
      { label: "发送升级信息失败,采集器没有响应,已完成", value: 81 },
    ],
  },
  {
    label: "已结束",
    value: 2,
    updateStatus: [
      { label: "未开始,已手动结束", value: 0 },
      { label: "版本发送成功,已结束", value: 1 },
      { label: "版本发送失败,已结束", value: 2 },
      { label: "中继加载失败,已结束", value: 4 },
      { label: "升级成功,已结束", value: 5 },
      { label: "升级失败,已结束", value: 6 },
      { label: "发送升级信息成功,已结束", value: 7 },
      { label: "发送升级信息失败,已结束", value: 8 },
      { label: "版本发送失败,采集器没有响应,已结束", value: 21 },
      { label: "中继加载失败,采集器没有响应,已结束", value: 41 },
      { label: "升级部分成功,已结束", value: 55 },
      { label: "升级失败,采集器没有响应,已结束", value: 61 },
      { label: "发送升级信息部分成功,已结束", value: 77 },
      { label: "发送升级信息失败,采集器没有响应,已结束", value: 81 },
    ],
  },
  { label: "准备重试", value: 3 },
  { label: "准备查询版本", value: 4 },
  { label: "查询版本，采集器没有返回", value: 5 },
  { label: "待审批", value: 10 },
  { label: "审批拒绝", value: 11 },
];

onBeforeMount(() => {
  tableData.columns = upgradeTaskColumn;
  handleQuery();
});

let dataList = ref([]);
async function handleQuery(condition) {
  let res = await getDataList("updateTask/queryUpdateTaskList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      dataList.value = reModel.data || [];
      tableData.dataSource =
        reModel.data?.map((e) => {
          let taskStatus = "";
          let ts = taskStatusOpt.find((v) => v.value == e.taskStatus);
          if (ts && !ts.updateStatus) taskStatus = ts.label;
          else if (ts && ts.updateStatus?.length)
            taskStatus =
              ts.updateStatus.find((v) => v.value == e.updateStatus)?.label ||
              "";

          return {
            ...e,
            taskStatus,
            type: typeOpt.find((v) => v.value == e.type)?.label || "",
          };
        }) || [];
    }
    if (condition) {
      if (code === 0) message.success(locales.value.caozuochenggong);
      else message.warning(locales.value.caozuoshibai);
    }
  }
}

let flag = ref({});
let viewData = ref([]);
let formData = ref([
  {
    label: locales.value.banbenbiaoshi,
    key: "versionId",
    value: undefined,
    type: "input",
    slot: true,
    disabled: true,
    required: true,
  },
  {
    label: locales.value.caijiqiimei,
    key: "imei",
    value: undefined,
    type: "input",
    slot: true,
    disabled: true,
    required: true,
  },
  {
    label: locales.value.zujianbiaoshi,
    key: "componentId",
    value: undefined,
    type: "input",
    slot: true,
    disabled: true,
    required: true,
  },
  {
    label: locales.value.renwukaishishijian,
    key: "updateBeginTime",
    value: undefined,
    type: "date",
    required: true,
  },
]);

async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    let curData = dataList.value.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    let {
      boardType,
      mcuSupport,
      bomSupport,
      fileName,
      bootPartition,
      relayId,
      beforeVersion,
    } = versionItem.value;
    Object.assign(data, {
      type: boardType || "",
      mcuSupport: mcuSupport || "",
      bomSupport: bomSupport || "",
      fileName: fileName || "",
      bootPartition: bootPartition || "",
      relayId: relayId || "",
      taskId: flag.value.value == "add" ? "" : curData.taskId,
      beforeVersion: beforeVersion || "",
      id: flag.value.value == "add" ? "" : tableData.selectedRowKeys[0],
    });
    let res = await saveOrUpdate("updateTask", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

let hasSelection = ref(false);
let addOrUpdate = ref("");
let versionItem = ref({}); //选择的版本标识信息
async function handleEvent(type) {
  if (type != "add" && type != "refresh") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
  versionItem.value = {};
  if (addOrUpdate.value) addOrUpdate.value = "";
  if (hasSelection.value) hasSelection.value = false;
  if (type == "add") {
    // 新增
    if (tableData.dataSource.length) {
      // 列表中有未结束的任务 终止新增
      let i = tableData.dataSource.findIndex(
        (e) =>
          e.taskStatus != undefined && e.taskStatus != 2 && e.taskStatus != 11
      );
      if (i == -1)
        return message.info("列表中有未结束的任务，请先结束任务再新增!");
    }
    formData.value[2].btnDis = false;
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (addOrUpdate.value != "add") v.value = undefined;
    });
    addOrUpdate.value = "add";
  } else if (type == "update") {
    // 修改
    let curData = dataList.value.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (curData.taskStatus != 10)
      return message.info("只有待审批的任务才能进行修改!");

    addOrUpdate.value = "update";
    formData.value[2].btnDis = false;

    let item = null;
    let res = await getTaskDetails({ taskId: curData.taskId });
    if (res?.data?.code === 0) item = res.data.reModel;

    flag.value = { label: locales.value.xiugai, value: "update" };
    formData.value.forEach((v) => {
      if (v.key == "versionId" && item) v.value = item.versionId;
      else if (v.key == "componentId" && item)
        v.value = item.componentIds?.join(",") || "";
      else v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    let item = null;
    let res = await getTaskDetails({ taskId: curData.taskId });
    if (res?.data?.code === 0) item = res.data.reModel;

    flag.value = { label: locales.value.chakan, value: "view" };

    viewData.value =
      formData.value.map((v) => {
        if (v.key == "versionId" && item) v.value = item.versionId;
        else if (v.key == "componentId" && item)
          v.value = item.componentIds?.join(",") || "";
        else v.value = curData[v.key];

        let { required, ...others } = v;
        return {
          ...others,
          disabled: true,
        };
      }) || [];

    let { fileName, type, taskStatus, updateTime } = curData;
    let arr = [
      {
        label: locales.value.banbenwenjian,
        key: "fileName",
        value: fileName,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.banbenleixing,
        key: "type",
        value: type,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.renwuzhuangtai,
        key: "taskStatus",
        value: taskStatus,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chaungjianshijian,
        key: "updateTime",
        value: updateTime,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let curData =
          dataList.value.find((e) => e.id == tableData.selectedRowKeys[0]) ||
          {};
        let res = await deleteUpdateTask({
          id: tableData.selectedRowKeys.join(","),
          taskId: curData.taskId || "",
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "refresh") {
    // 刷新
    handleQuery(true);
  } else if (type == "retry") {
    // 重试
    let i = dataList.value.findIndex(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (i > -1) {
      if (i !== 0) return message.info("只有最近的升级任务才能重试!");
      const { taskStatus, updateStatus, taskId } = dataList.value[i];
      if (taskStatus == 1 || taskStatus == 5) {
        if (updateStatus == 5 || updateStatus == 7)
          return message.info("任务不是失败状态，无需重试!");
      } else return message.info("任务不是已完成状态，无需重试!");

      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = await retryUpdateTask({ taskId });
          if (res?.data?.code === 0) {
            message.success(locales.value.caozuochenggong);
            handleQuery();
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    }
  } else if (type == "end") {
    // 结束任务
    let i = dataList.value.findIndex(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (i > -1) {
      if (i !== 0) return message.info("只有最近的升级任务才能结束!");
      const { taskStatus, taskId } = dataList.value[i];
      if (taskStatus == 2) return message.info("任务已结束，无需再次结束!");
      if (taskStatus != 0 && taskStatus != 1 && taskStatus != 5)
        return message.info("任务当前状态不能结束，请稍后再结束!");

      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = await endUpdateTask({ taskId });
          if (res?.data?.code === 0) {
            message.success(locales.value.caozuochenggong);
            handleQuery();
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    }
  } else if (type == "query-version") {
    // 升级版本查询
    let i = dataList.value.findIndex(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    if (i > -1) {
      if (i !== 0) return message.info("只有最近的升级任务才能査询!");
      const { taskStatus, taskId } = dataList.value[i];
      if (taskStatus == 2) return message.info("任务已结束，不能进行查询!");
      if (taskStatus != 1 && taskStatus != 5)
        return message.info("任务当前状态不能进行查询，请稍后再进行查询!");

      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = await versionQuery({ taskId });
          if (res?.data?.code === 0) {
            message.success(locales.value.caozuochenggong);
            handleQuery();
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    }
  } else if (type == "view-result") {
    // 查看升级结果
    let curData =
      dataList.value.find((e) => e.id == tableData.selectedRowKeys[0]) || {};
    let { taskStatus, taskId } = curData;
    if (taskStatus == 10 || taskStatus == 11)
      return message.info("任务没有开始，不能查看升级结果!");

    flag.value = { label: locales.value.chakanshengjijieguo, value: "deep" };
    curApi.value = updateTaskResult;
    params.value = {
      taskId,
    };
    deepColumn.value = updateResultColumn;
    btns.value = [
      { label: locales.value.shuaxin, value: "refresh" },
      { label: locales.value.fanhui, value: "back" },
    ];
  }
}

let btns = ref([]);
let curApi = ref(null);
let params = ref({});
let rowKey = ref("id");
let deepColumn = ref([]);
let refresh = ref(false);
let searchDeepData = ref([]);
async function handleComponentChoose(type) {
  hasSelection.value = true;
  rowKey.value = "id";
  if (type == "component") {
    // 选择组件
    if (!formData.value.find((e) => e.key == "versionId")?.value)
      return message.warning(locales.value.qingxuanzebanben);

    // 只有优化器才有表格筛选
    if (versionItem.value.boardType == "optimizer")
      searchDeepData.value = [
        {
          label: locales.value.zujianbiaoshi,
          key: "chipId",
          value: undefined,
          type: "input",
        },
        {
          label: locales.value.zuchuanname,
          key: "groupName",
          value: undefined,
          type: "input",
        },
      ];
    else searchDeepData.value = [];

    rowKey.value = "chipId";

    let curData =
      dataList.value.find((e) => e.id == tableData.selectedRowKeys[0]) || {};

    flag.value = { label: locales.value.xuanzezujian, value: "deep" };
    curApi.value = getTaskComponent;
    params.value = {
      type: versionItem.value.boardType || curData.type || "",
      mcuSupport: versionItem.value.mcuSupport || curData.mcuSupport || "",
      bomSupport: versionItem.value.bomSupport || curData.bomSupport || "",
      bootPartition:
        versionItem.value.bootPartition || curData.bootPartition || "",
      id: "",
      imei: "",
    };
    deepColumn.value = updateComponentColumn;
    btns.value = [
      { label: locales.value.queren, value: "component-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "version") {
    // 选择版本
    flag.value = { label: locales.value.xuanzebanben, value: "deep" };
    curApi.value = getTaskVersion;
    params.value = {};
    deepColumn.value = updateVersionColumn;
    btns.value = [
      { label: locales.value.queren, value: "version-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "imei") {
    // 选择采集器
    if (!formData.value.find((e) => e.key == "versionId")?.value)
      return message.warning(locales.value.qingxuanzebanben);

    flag.value = { label: locales.value.xuanzecaijiqi, value: "deep" };
    curApi.value = selectCloudTerminal;
    params.value = {
      selectInverter: "",
      updateTaskId: "",
    };
    deepColumn.value = updateImeiColumn;
    btns.value = [
      { label: locales.value.queren, value: "imei-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  }
}
function handleBtnClick({ type, value, items }) {
  if (refresh.value) refresh.value = false;
  if (type == "back")
    flag.value = addOrUpdate.value
      ? {
          label:
            addOrUpdate.value == "add"
              ? locales.value.xinzeng
              : locales.value.xiugai,
          value: addOrUpdate.value,
        }
      : {};
  else if (type == "refresh") {
    // 刷新
    refresh.value = true;
    message.success(locales.value.caozuochenggong);
  } else if (type == "version-confirm") {
    // 确认选择版本
    if (items?.length !== 1)
      return message.warning(locales.value.zhengquexuanzecaozuoxiang);

    versionItem.value = items?.[0] || {};
    formData.value.forEach((e) => {
      if (e.key == "versionId") e.value = items?.[0]?.versionId || "";
      if (e.key == "imei" || e.key == "componentId") e.value = "";
    });
    flag.value = {
      label:
        addOrUpdate.value == "add"
          ? locales.value.xinzeng
          : locales.value.xiugai,
      value: addOrUpdate.value,
    };
  } else if (type == "imei-confirm") {
    // 确认选择采集器
    if (items?.length !== 1)
      return message.warning(locales.value.zhengquexuanzecaozuoxiang);

    formData.value.forEach((e) => {
      if (e.key == "imei") e.value = items?.[0]?.imei || "";
    });
    flag.value = {
      label:
        addOrUpdate.value == "add"
          ? locales.value.xinzeng
          : locales.value.xiugai,
      value: addOrUpdate.value,
    };
  } else if (type == "component-confirm") {
    // 确认选择组件
    if (items?.length != 1)
      return message.warning(locales.value.zhengquexuanzecaozuoxiang);

    formData.value.forEach((e) => {
      if (e.key == "componentId")
        e.value = items.map((v) => v.chipId)?.join(",") || "";
    });
    versionItem.value.beforeVersion = items[0].softVersion;
    versionItem.value.bootPartition = items[0].bootPartition;
    versionItem.value.relayId = items[0].relayId;

    flag.value = {
      label:
        addOrUpdate.value == "add"
          ? locales.value.xinzeng
          : locales.value.xiugai,
      value: addOrUpdate.value,
    };
  }
}
</script>

<style lang="less" scoped>
.upgrade-task {
  .btns {
    margin-bottom: 12px;
  }
  .btn {
    width: 82px !important;
    flex-shrink: 0 !important;
  }
}
</style>