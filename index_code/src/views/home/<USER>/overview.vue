<template>
  <div class="overview w100 plr16">
    <div class="top w100">
      <div class="header flex-row-center-between">
        <div class="topic flex-row-center-start nowrap">
          <img src="@/assets/images/dianzhan-2.png" alt="" />
          <div class="title mlr16">隆基别墅关断测试电站</div>
          <span class="status pl16">正常</span>
        </div>
        <div class="info flex-row-center-start">
          <div class="l flex-column-center-start">
            <img src="@/assets/images/tianqi-1.png" alt="" />
            <span>阴天</span>
          </div>
          <div class="r flex-column ml16">
            <span class="date">周六 2025-01-25</span>
            <span class="temperature">-6 °C</span>
          </div>
        </div>
      </div>
      <div class="container flex-row-center-between nowrap">
        <div class="info-item flex-row-center-start">
          <img src="@/assets/images/overview.png" alt="" />
          <div class="detail">
            <div class="flex-row-end-start nowrap">
              <div class="value">245.34</div>
              <div class="unit">kW</div>
            </div>
            <div class="label">当前功率</div>
          </div>
        </div>
        <div class="info-item flex-row-center-start">
          <img src="@/assets/images/overview.png" alt="" />
          <div class="detail">
            <div class="flex-row-end-start nowrap">
              <div class="value">89.37</div>
              <div class="unit">度</div>
            </div>
            <div class="label">当日发电量</div>
          </div>
        </div>
        <div class="info-item flex-row-center-start">
          <img src="@/assets/images/overview.png" alt="" />
          <div class="detail">
            <div class="flex-row-end-start nowrap">
              <div class="value">389.37</div>
              <div class="unit">度</div>
            </div>
            <div class="label">当月发电量</div>
          </div>
        </div>
        <div class="info-item flex-row-center-start">
          <img src="@/assets/images/overview.png" alt="" />
          <div class="detail">
            <div class="flex-row-end-start nowrap">
              <div class="value">09.59</div>
              <div class="unit">万度</div>
            </div>
            <div class="label">累计发电量</div>
          </div>
        </div>
      </div>
    </div>
    <div class="main w100">
      <div class="left1 flex-row-center-between">
        <div class="title flex-row-center-center nowrap">
          <img src="@/assets/images/shuaxin.png" alt="" />
          <span>实时刷新</span>
        </div>
        <div class="part1">
          <div class="top w100 flex-row-center-center">
            <div class="circle flex-row-center-center">
              <div class="flex-column-center-center">
                <img src="@/assets/images/guangfu.png" alt="" />
                <span class="value">12.34</span>
                <span class="unit">kW</span>
                <span class="label">光伏</span>
              </div>
            </div>
          </div>
          <div class="center w100 flex-row-center-between">
            <div class="circle l flex-row-center-center">
              <div class="flex-column-center-center">
                <img src="@/assets/images/chuneng.png" alt="" />
                <span class="value">0.47</span>
                <span class="unit">kW</span>
                <span class="label">储能</span>
              </div>
            </div>
            <div class="circle r flex-row-center-center">
              <div class="flex-column-center-center">
                <img src="@/assets/images/dianwang.png" alt="" />
                <span class="value">0.12</span>
                <span class="unit">kW</span>
                <span class="label">电网</span>
              </div>
            </div>
          </div>
          <div class="bottom w100 flex-row-center-center">
            <div class="circle flex-row-center-center">
              <div class="flex-column-center-center">
                <img src="@/assets/images/fuzai.png" alt="" />
                <span class="value">7.43</span>
                <span class="unit">kW</span>
                <span class="label">负载</span>
              </div>
            </div>
          </div>
          <div class="line1"></div>
          <div class="line2"></div>
          <div class="line3"></div>
        </div>
        <div class="part2">
          <div class="item">
            <div class="flex-row-end-start nowrap">
              <div class="value">1.47</div>
              <div class="unit">度</div>
            </div>
            <div class="label">当日充电电量</div>
          </div>
          <div class="item">
            <div class="flex-row-end-start nowrap">
              <div class="value">0.36</div>
              <div class="unit">度</div>
            </div>
            <div class="label">当日放电电量</div>
          </div>
          <div class="item">
            <div class="flex-row-end-start nowrap">
              <div class="value">20.36</div>
              <div class="unit">吨</div>
            </div>
            <div class="label">减排CO2总量</div>
          </div>
          <div class="item">
            <div class="flex-row-end-start nowrap">
              <div class="value">46.39</div>
              <div class="unit">吨</div>
            </div>
            <div class="label">节约标准煤总量</div>
          </div>
        </div>
      </div>
      <div class="right1 flex-row-center-center">
        <HBar class="h100" style="width: 360px" />
        <div class="info h100 flex-column-start-between">
          <div>
            <div class="label">电站地址：</div>
            <div class="value nowrap">中国，江苏，南京，瑞丰街道</div>
          </div>
          <div>
            <div class="label">创建时间：</div>
            <div class="value">2025-03-12</div>
          </div>
          <div>
            <div class="label">运行天数：</div>
            <div class="value">564 天</div>
          </div>
        </div>
      </div>
      <Line :chartsData="chartsData" />
      <VBar />
    </div>
  </div>
</template>

<script setup>
import HBar from "../../../components/HBar.vue";
import VBar from "../../../components/VBar.vue";
import Line from "../../../components/Line.vue";

let chartsData = {
  dataX: [
    "00:00",
    "01:00",
    "02:00",
    "03:00",
    "04:00",
    "05:00",
    "06:00",
    "07:00",
  ],
  dataS: [2, 8, 10, 13, 12, 21, 35, 40],
  data: [
    { time: "00:00", value: 2 },
    { time: "01:00", value: 8 },
  ],
};
</script>

<style lang="less" scoped>
.overview {
  > .top {
    height: 270px;
    padding: 20px 24px;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    .header {
      .topic {
        > img {
          width: 42px;
          height: 42px;
        }
        .title {
          font-size: 20px;
          font-weight: 500;
          color: #0d0c12;
        }
        .status {
          position: relative;
          font-size: 16px;
          color: #43cf7c;
          line-height: 22px;
          &::before {
            position: absolute;
            top: 7px;
            left: 0;
            content: "";
            width: 8px;
            height: 8px;
            background-color: #43cf7c;
            border-radius: 50%;
          }
        }
      }
      > .info {
        .l {
          > img {
            // width: 35px;
            height: 33px;
          }
          > span {
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            color: #383838;
          }
        }
        .r {
          .date {
            font-size: 12px;
            font-weight: 500;
            line-height: 17px;
            color: #808080;
          }
          .temperature {
            font-size: 20px;
            font-weight: 500;
            line-height: 28px;
            color: #165dff;
            margin-top: 2px;
          }
        }
      }
    }
    .container {
      margin-top: 42px;
      .info-item {
        width: 359px;
        height: 120px;
        padding: 13px 0;
        margin-left: 16px;
        border-radius: 10px;
        background: #f7f7f7;
        &:first-child {
          margin-left: 0;
        }
        > img {
          width: 90px;
          height: 94px;
          margin: 0 32px;
        }
        .detail {
          .value {
            font-size: 32px;
            font-weight: 700;
            color: #383838;
            line-height: 44px;
            margin-right: 5px;
          }
          .unit {
            font-size: 20px;
            color: #383838;
            line-height: 36px;
          }
          .label {
            margin-top: 2px;
            font-size: 24px;
            line-height: 33px;
            color: #a6a6a6;
          }
        }
      }
    }
  }
  .main {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-gap: 24px;
    margin-top: 23px;
    > div {
      height: 558px;
      background-color: #fff;
      border-radius: 6px;
      overflow: hidden;
    }
    .left1 {
      position: relative;
      padding: 28px 24px;
      .title {
        position: absolute;
        left: 24px;
        top: 28px;
        width: 109px;
        height: 42px;
        border-radius: 21px;
        background: #f7f7f7;
        > img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        > span {
          font-size: 16px;
          font-weight: 500;
          color: #808080;
        }
      }
      .part1 {
        position: relative;
        width: 466px;
        height: 466px;
        padding-top: 34px;
        .circle {
          position: relative;
          width: 122px;
          height: 122px;
          border-radius: 50%;
          > div {
            width: 100px;
            height: 100px;
            background: #fff;
            border-radius: 50%;
            .label {
              position: absolute;
              bottom: -48px;
              left: 50%;
              transform: translateX(-50%);
              font-size: 20px;
              font-weight: 500;
              line-height: 28px;
              color: #383838;
            }
            .value {
              font-size: 16px;
              font-weight: 700;
              color: #383838;
              margin: 2px 0;
            }
            .unit {
              font-size: 16px;
              font-weight: 500;
              line-height: 22px;
              color: #a6a6a6;
            }
            > img {
              width: 32px;
              height: 32px;
            }
          }
        }
        .top {
          height: 122px;
          .circle {
            border: 1px solid rgba(255, 195, 0, 0.37);
            > div {
              border: 1px solid rgba(255, 195, 0, 0.8);
              box-shadow: 0px 0px 5px 2px rgba(255, 195, 0, 0.23);
              .label {
                top: -48px;
                bottom: unset;
              }
            }
          }
        }
        .bottom {
          height: 122px;
          .circle {
            border: 1px solid rgba(42, 130, 228, 0.37);
            > div {
              border: 1px solid rgba(42, 130, 228, 0.62);
              box-shadow: 0px 0px 5px 2px rgba(42, 130, 228, 0.32);
            }
          }
        }
        .center {
          height: 122px;
          margin: 16px 0;
          .circle {
            &.l {
              border: 1px solid rgba(0, 186, 173, 0.37);
              > div {
                border: 1px solid rgba(0, 186, 173, 0.62);
                box-shadow: 0px 0px 5px 2px rgba(0, 186, 173, 0.32);
              }
            }
            &.r {
              border: 1px solid rgba(172, 51, 193, 0.37);
              > div {
                border: 1px solid rgba(172, 51, 193, 0.8);
                box-shadow: 0px 0px 5px 2px rgba(172, 51, 193, 0.19);
              }
            }
          }
        }
        .line1 {
          position: absolute;
          top: 145px;
          left: calc(50% - 1px);
          width: 2px;
          height: 176px;
          background: linear-gradient(
            180deg,
            rgba(255, 207, 51, 1) 0%,
            rgba(242, 250, 155, 1) 50.76%,
            rgba(123, 178, 238, 1) 100%
          );
          &::before {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            content: "";
            border-top: 9px solid #ffc300;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
          }
        }
        .line2 {
          position: absolute;
          top: 142px;
          left: 111px;
          width: calc(50% - 124px);
          height: 100px;
          border: 2px solid;
          border-image: linear-gradient(225deg, #fed23b, #61d4cc) 2;
          border-top: none;
          border-left: none;
          border-bottom-right-radius: 14px;
          &::before {
            position: absolute;
            top: 40px;
            right: -9px;
            content: "";
            border-top: 9px solid #6ac9d7;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            z-index: 1;
          }
          background: transparent;
          border-radius: 0 0 14px 0; /* 只设置右下角圆角 */
          // &::after {
          //   content: "";
          //   position: absolute;
          //   top: 0;
          //   left: 0;
          //   right: 0;
          //   bottom: 0;
          //   border-radius: 0 0 14px 0; /* 右下角圆角 */
          //   background: linear-gradient(
          //       to bottom left,
          //       #fed23b 0%,
          //       #61d4cc 100%
          //     ),
          //     linear-gradient(to bottom right, transparent 50%, #fff 100%);
          //   background-position: 100% 0, 100% 100%; /* 右边框和下边框的位置 */
          //   background-size: 2px 100%, 100% 2px; /* 右边框宽度2px，下边框高度2px */
          //   background-repeat: no-repeat; /* 禁止重复 */
          // }
        }
        .line3 {
          position: absolute;
          top: 236px;
          right: 111px;
          width: calc(50% - 124px);
          height: 87px;
          border: 2px solid;
          border-image: linear-gradient(205deg, #bd5ccd, #7eaeed, #7bb2ee) 2;
          border-right: none;
          border-bottom: none;
          border-top-left-radius: 14px;
          &::before {
            position: absolute;
            top: 34px;
            left: -9px;
            content: "";
            border-top: 9px solid #a976d7;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
          }
        }
      }
      .part2 {
        padding-top: 50px;
        .item {
          margin-bottom: 36px;
          .value {
            font-size: 32px;
            font-weight: 700;
            color: #383838;
            line-height: 44px;
            margin-right: 5px;
          }
          .unit {
            font-size: 20px;
            color: #383838;
            line-height: 36px;
          }
          .label {
            margin-top: 2px;
            font-size: 24px;
            line-height: 33px;
            color: #a6a6a6;
          }
        }
      }
    }
    .right1 {
      padding: 59px 48px 50px;
      .info {
        margin-left: 80px;
        padding: 15px 0 40px;
        > div {
          .label {
            font-size: 16px;
            color: #0d0c12;
            font-weight: 600;
          }
          .value {
            font-size: 16px;
            color: #0d0c12;
            margin-top: 30px;
          }
        }
      }
    }
  }
}
</style>