// no baseURI

// object to store loaded and loading chunks
// undefined = chunk not loaded, null = chunk preloaded/prefetched
// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
var installedChunks = {
	524: 0
};

__webpack_require__.f.j = function(chunkId, promises) {
		// JSONP chunk loading for javascript
		var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
		if(installedChunkData !== 0) { // 0 means "already installed".

			// a Promise means "currently loading".
			if(installedChunkData) {
				promises.push(installedChunkData[2]);
			} else {
				if(true) { // all chunks have JS
					// setup Promise in chunk cache
					var promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });
					promises.push(installedChunkData[2] = promise);

					// start chunk loading
					var url = __webpack_require__.p + __webpack_require__.u(chunkId);
					// create error before stack unwound to get useful stacktrace later
					var error = new Error();
					var loadingEnded = function(event) {
						if(__webpack_require__.o(installedChunks, chunkId)) {
							installedChunkData = installedChunks[chunkId];
							if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
							if(installedChunkData) {
								var errorType = event && (event.type === 'load' ? 'missing' : event.type);
								var realSrc = event && event.target && event.target.src;
								error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
								error.name = 'ChunkLoadError';
								error.type = errorType;
								error.request = realSrc;
								installedChunkData[1](error);
							}
						}
					};
					__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
				}
			}
		}
};

// no prefetching

// no preloaded

// no HMR

// no HMR manifest

__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };

// install a JSONP callback for chunk loading
var webpackJsonpCallback = function(parentChunkLoadingFunction, data) {
	var chunkIds = data[0];
	var moreModules = data[1];
	var runtime = data[2];
	// add "moreModules" to the modules object,
	// then flag all "chunkIds" as loaded and fire callback
	var moduleId, chunkId, i = 0;
	if(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {
		for(moduleId in moreModules) {
			if(__webpack_require__.o(moreModules, moduleId)) {
				__webpack_require__.m[moduleId] = moreModules[moduleId];
			}
		}
		if(runtime) var result = runtime(__webpack_require__);
	}
	if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
	for(;i < chunkIds.length; i++) {
		chunkId = chunkIds[i];
		if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
			installedChunks[chunkId][0]();
		}
		installedChunks[chunkId] = 0;
	}
	return __webpack_require__.O(result);
}

var chunkLoadingGlobal = self["webpackChunkvisual_drag_demo"] = self["webpackChunkvisual_drag_demo"] || [];
chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));