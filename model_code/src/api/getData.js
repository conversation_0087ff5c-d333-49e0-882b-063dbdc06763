import base from './index'
let axios = base.axios
let baseUrl = base.baseUrl
    // let tokens
let token

// const queryParam = new URLSearchParams(window.location.search);;
// // console.log(queryParam.get('token'));

// let geturl = window.location.href
// let getqyinfo = geturl.split('?')[1] //code=abc&name=%E4%BC%81%E4%B8%9A%E5%BF%99   截取到参数部分
// let getqys = new URLSearchParams('?' + getqyinfo) //将参数放在URLSearchParams函数中

// // console.log('--ok', getqys.get('token'))
// if (getqys.get('token')) {
//     tokens = getqys.get('token')
//     localStorage.setItem('token', tokens)
// } else {
//     tokens = ''
// }
// token = localStorage.getItem('token')

//优化器新增
export const SaveOrUpdateChangeDto = params => {

    return axios({
        method: 'post',
        baseURL: `${baseUrl}/systemView/saveOrUpdateChangeDto.web`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}


//组串移动，新增和修改哪个
export const saveOrUpdateGroup = params => {
    console.log('params', JSON.stringify(params))
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/systemView/saveOrUpdateGroupView.web`,
        data: JSON.stringify(params),
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

export const UpdateChange = params => {
    // console.log('params', params)
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/systemView/saveOrUpdateChangeDto.web`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}



export const QueryChange = params => {
    // console.log('params', params)
    return axios({
        method: 'get',
        baseURL: `${baseUrl}/systemView/queryChangeDtoDetail.web?id=` + params.id,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}



//设置位置信息
export const Moveupdatedata = params => {
    // console.log('params', params)
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/systemView/moveupdatedata.htm`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}

//获取信息
export const StationList = params => {
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/component/queryComponentListTo.web`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}

//用户登录
export const doLogin = params => {
    // console.log('url', `${baseUrl}/free/check.web`, )
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/free/check.web`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}

export const queryPowerStationList = params => {
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/powerstation/queryPowerStationList.web`,
        data: params,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }).then(res => res.data)
}



// 获取好友
export const getFriend = params => {
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/friend/friendList`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

// 获取聊天信息
export const getChatMsg = params => {
    // console.log("baseUrl", baseUrl + '/friend/chatMsg')
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/friend/chatMsg`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//AI获取模型列表
export const getTxtPicModel = params => {
    return axios({
        method: 'get',
        baseURL: `${baseUrl}/txt2pic/models`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//文生图
export const getTxtPic = params => {
    // console.log("baseUrl", baseUrl + '/txt2pic/generate')
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/txt2pic/generate`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//图生图
export const getPic = params => {
        return axios({
            method: 'post',
            baseURL: `${baseUrl}/pic2pic/generate`,
            data: params,
            headers: {
                'Authorization': `${token}`,
                'Content-Type': 'application/json;charset=UTF-8'
            }
        }).then(res => res.data)
    }
    //训练模型
export const getLoraTrain = params => {
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/lora/train`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//删除模型
export const deleteModel = params => {
    return axios({
        method: 'delete',
        baseURL: `${baseUrl}/lora/models/${params.model_id}`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//使用lora模型
export const lora_generate = params => {
    return axios({
        method: 'post',
        baseURL: `${baseUrl}/lora/generate`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//使用lora模型
export const getModel = params => {
    return axios({
        method: 'get',
        baseURL: `${baseUrl}/lora/models`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

export const getModels = params => {
    return axios({
        method: 'get',
        baseURL: `${baseUrl}/lora/models?id=` + params.id,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}




//获取聊天记录  /api/histories
export const getHistories = params => {
    return axios({
        method: 'get',
        baseURL: `${baseUrl}/histories?job_type=` + params.job_type + `&reverse=true`,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}

//删除聊天记录  /api/histories/{job_id}
export const delHistories = params => {
    return axios({
        method: 'delete',
        baseURL: `${baseUrl}/histories/` + params.job_id,
        data: params,
        headers: {
            'Authorization': `${token}`,
            'Content-Type': 'application/json;charset=UTF-8'
        }
    }).then(res => res.data)
}