import axios from 'axios'
import {
    MessageBox
} from 'element-ui';
//全局参数，自定义参数可在发送请求时设置
axios.defaults.timeout = 300000000 //超时时间ms
axios.defaults.withCredentials = true
// 请求时的拦截
//回调里面不能获取错误信息
axios.interceptors.request.use(
    function (config) {

        return config;
    },
    function (error) {
        // 当请求异常时做一些处理
        // console.log('请求异常：' + JSON.stringify(error));
        return Promise.reject(error);
    }
);

axios.interceptors.response.use(function (response) {
    // Do something with response data
    // console.log('response.headers', response.headers)
    return response
}, function (error) {
    // Do something with response error
    // console.log('响应出错：' + error.get('status'))

    // console.log('响应出错' + JSON.parse(JSON.stringify(error)).status);
    if (JSON.parse(JSON.stringify(error)).status == '401') {

        // alert('ok')
        // MessageBox.confirm('登陆状态已失效，请从登陆页面重新登陆，点击关闭当前页面。', '', {
        //     showCancelButton: true,
        //     showConfirmButton: false, // 隐藏确认按钮
        //     cancelButtonText: '确认'
        // }).then(() => {
        //     // 确认操作
        //     console.log('Confirmed!');
        // }).catch(() => {
        //     // 取消操作
        //     // console.log('Cancelled!');
        //     window.location.href = 'http://10.1.247.23:808'

        //     return
        // });
        // window.location.href = 'http://10.1.247.23:808'
    }
    return Promise.reject(error)
})


const base = {
    axios: axios,
    baseUrl: '/photovoltaic-manager'
}

export default base
