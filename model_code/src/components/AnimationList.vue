<template>
  <div class="animation-list">
    <div class="animation-header">
      <h4>动画配置</h4>
      <el-button size="mini" type="primary" @click="addAnimation">
        <i class="el-icon-plus" /> 添加动画
      </el-button>
    </div>
    
    <div class="animation-items">
      <div 
        v-for="(animation, index) in animations" 
        :key="index"
        class="animation-item"
      >
        <div class="animation-header-item">
          <span class="animation-name">{{ animation.name }}</span>
          <div class="animation-actions">
            <el-button 
              size="mini" 
              type="text" 
              @click="playAnimation(animation)"
            >
              <i class="el-icon-video-play" />
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click="editAnimation(animation, index)"
            >
              <i class="el-icon-edit" />
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click="deleteAnimation(index)"
            >
              <i class="el-icon-delete" />
            </el-button>
          </div>
        </div>
        
        <div class="animation-config">
          <el-form size="mini" label-width="60px">
            <el-form-item label="类型">
              <el-select v-model="animation.type" size="mini">
                <el-option label="淡入淡出" value="fade" />
                <el-option label="滑动" value="slide" />
                <el-option label="缩放" value="scale" />
                <el-option label="旋转" value="rotate" />
                <el-option label="弹跳" value="bounce" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="持续时间">
              <el-input-number 
                v-model="animation.duration" 
                :min="0.1" 
                :max="10" 
                :step="0.1"
                size="mini"
              />
              <span style="margin-left: 5px; font-size: 12px;">秒</span>
            </el-form-item>
            
            <el-form-item label="延迟">
              <el-input-number 
                v-model="animation.delay" 
                :min="0" 
                :max="5" 
                :step="0.1"
                size="mini"
              />
              <span style="margin-left: 5px; font-size: 12px;">秒</span>
            </el-form-item>
            
            <el-form-item label="缓动">
              <el-select v-model="animation.easing" size="mini">
                <el-option label="线性" value="linear" />
                <el-option label="缓入" value="ease-in" />
                <el-option label="缓出" value="ease-out" />
                <el-option label="缓入缓出" value="ease-in-out" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="循环">
              <el-switch v-model="animation.loop" />
            </el-form-item>
            
            <el-form-item v-if="animation.loop" label="循环次数">
              <el-input-number 
                v-model="animation.iterations" 
                :min="1" 
                :max="100"
                size="mini"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    
    <div v-if="animations.length === 0" class="empty-state">
      <i class="el-icon-magic-stick" />
      <p>暂无动画配置</p>
      <el-button size="small" type="primary" @click="addAnimation">
        添加第一个动画
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AnimationList',
  computed: {
    ...mapState(['curComponent']),
    
    animations: {
      get() {
        return this.curComponent?.animations || []
      },
      set(value) {
        if (this.curComponent) {
          this.$set(this.curComponent, 'animations', value)
        }
      }
    }
  },
  methods: {
    addAnimation() {
      const newAnimation = {
        name: `动画${this.animations.length + 1}`,
        type: 'fade',
        duration: 1,
        delay: 0,
        easing: 'ease-in-out',
        loop: false,
        iterations: 1
      }
      
      if (!this.curComponent.animations) {
        this.$set(this.curComponent, 'animations', [])
      }
      
      this.animations.push(newAnimation)
    },
    
    editAnimation(animation, index) {
      // 编辑动画逻辑
      console.log('编辑动画:', animation, index)
    },
    
    deleteAnimation(index) {
      this.$confirm('确定要删除这个动画吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.animations.splice(index, 1)
        this.$message.success('动画已删除')
      }).catch(() => {
        // 取消删除
      })
    },
    
    playAnimation(animation) {
      // 播放动画预览
      this.$message.info(`播放动画: ${animation.name}`)
      // 这里可以添加实际的动画播放逻辑
    }
  }
}
</script>

<style scoped>
.animation-list {
  padding: 15px;
  height: 100%;
  overflow-y: auto;
}

.animation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.animation-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.animation-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.animation-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.animation-header-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.animation-name {
  font-weight: 500;
  color: #303133;
}

.animation-actions {
  display: flex;
  gap: 5px;
}

.animation-config {
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 10px;
}

/* 暗色主题 */
.dark-theme .animation-header h4 {
  color: #ffffff;
}

.dark-theme .animation-item {
  border-color: #404040;
  background: #3a3a3a;
}

.dark-theme .animation-name {
  color: #ffffff;
}

.dark-theme .animation-config {
  border-top-color: #404040;
}
</style>
