<template>
  <div class="toolbar">
    <div class="toolbar-left">
      <el-button-group>
        <el-button size="small" icon="el-icon-back" @click="undo" :disabled="!canUndo">
          撤销
        </el-button>
        <el-button size="small" icon="el-icon-right" @click="redo" :disabled="!canRedo">
          重做
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button size="small" icon="el-icon-copy-document" @click="copy" :disabled="!curComponent">
          复制
        </el-button>
        <el-button size="small" icon="el-icon-document" @click="paste" :disabled="!copyData">
          粘贴
        </el-button>
        <el-button size="small" icon="el-icon-delete" @click="deleteComponent" :disabled="!curComponent">
          删除
        </el-button>
      </el-button-group>
    </div>
    
    <div class="toolbar-center">
      <span class="canvas-info">
        画布尺寸: {{ canvasStyleData.width }} × {{ canvasStyleData.height }}
      </span>
      
      <div class="scale-control">
        <el-button size="small" icon="el-icon-minus" @click="scaleDown"></el-button>
        <span class="scale-text">{{ canvasStyleData.scale }}%</span>
        <el-button size="small" icon="el-icon-plus" @click="scaleUp"></el-button>
      </div>
    </div>
    
    <div class="toolbar-right">
      <el-button-group>
        <el-button 
          size="small" 
          :type="editMode === 'edit' ? 'primary' : ''" 
          @click="setEditMode('edit')">
          编辑
        </el-button>
        <el-button 
          size="small" 
          :type="editMode === 'preview' ? 'primary' : ''" 
          @click="setEditMode('preview')">
          预览
        </el-button>
      </el-button-group>
      
      <el-button size="small" @click="clearCanvas">
        清空画布
      </el-button>
      
      <el-button size="small" type="primary" @click="saveCanvas">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'Toolbar',
  data() {
    return {
      copyData: null
    }
  },
  computed: {
    ...mapState([
      'curComponent',
      'canvasStyleData',
      'editMode',
      'componentData',
      'snapshotData',
      'snapshotIndex',
      'copyData'
    ]),

    canUndo() {
      return this.snapshotIndex >= 0
    },

    canRedo() {
      return this.snapshotIndex < this.snapshotData.length - 1
    }
  },
  
  methods: {
    undo() {
      this.$store.commit('undo')
      this.$message.success('撤销成功')
    },

    redo() {
      this.$store.commit('redo')
      this.$message.success('重做成功')
    },

    copy() {
      this.$store.commit('copy')
    },

    paste() {
      this.$store.commit('paste')
    },
    
    deleteComponent() {
      if (this.curComponent) {
        this.$store.commit('deleteComponent')
        this.$message.success('组件已删除')
      }
    },
    
    scaleUp() {
      const newScale = Math.min(this.canvasStyleData.scale + 10, 200)
      this.$store.commit('setCanvasStyle', {
        ...this.canvasStyleData,
        scale: newScale
      })
    },
    
    scaleDown() {
      const newScale = Math.max(this.canvasStyleData.scale - 10, 50)
      this.$store.commit('setCanvasStyle', {
        ...this.canvasStyleData,
        scale: newScale
      })
    },
    
    setEditMode(mode) {
      this.$store.commit('setEditMode', mode)
      this.$message.success(`已切换到${mode === 'edit' ? '编辑' : '预览'}模式`)
    },
    
    clearCanvas() {
      this.$confirm('确定要清空画布吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.commit('setComponentData', [])
        this.$store.commit('setCurComponent', { component: null, index: null })
        this.$message.success('画布已清空')
      }).catch(() => {})
    },
    
    saveCanvas() {
      const canvasData = {
        canvasStyleData: this.canvasStyleData,
        componentData: this.componentData
      }
      
      // 这里可以实现保存到服务器的逻辑
      console.log('保存画布数据:', canvasData)
      localStorage.setItem('canvasData', JSON.stringify(canvasData))
      this.$message.success('画布已保存到本地')
    },
    
    generateID() {
      return Date.now().toString(36) + Math.random().toString(36).substring(2)
    }
  }
}
</script>

<style scoped>
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.canvas-info {
  font-size: 12px;
  color: #606266;
}

.scale-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scale-text {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
  text-align: center;
}

/* 暗色主题 */
.dark-theme .toolbar {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.dark-theme .canvas-info,
.dark-theme .scale-text {
  color: #cccccc;
}
</style>
