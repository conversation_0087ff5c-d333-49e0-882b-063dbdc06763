<template>
  <div class="editor">
    <div class="editor-canvas" 
         :style="canvasStyle"
         @drop="handleDrop"
         @dragover="handleDragOver"
         @mousedown="handleMouseDown"
         @mouseup="deselectCurComponent">
      
      <!-- 画布内容 -->
      <div class="canvas-content" :style="canvasContentStyle">
        <!-- 渲染组件 -->
        <component
          v-for="(item, index) in componentData"
          :key="item.id"
          :is="item.component"
          :id="'component' + item.id"
          :element="item"
          :style="getShapeStyle(item.style)"
          :class="getComponentClass(item)"
          @click="handleClick(item, index)"
          @contextmenu="handleContextMenu($event, item)"
          @mousedown="handleMouseDown($event, item)"
        />
        
        <!-- 选中框 -->
        <div v-if="curComponent" 
             class="selection-box"
             :style="selectionBoxStyle">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { generateID } from '@/utils/generateID'

export default {
  name: 'Editor',
  computed: {
    ...mapState([
      'componentData',
      'curComponent',
      'curComponentIndex',
      'canvasStyleData'
    ]),
    
    canvasStyle() {
      return {
        width: '100%',
        height: '100%',
        background: this.canvasStyleData.background,
        position: 'relative',
        overflow: 'hidden'
      }
    },
    
    canvasContentStyle() {
      return {
        width: this.canvasStyleData.width + 'px',
        height: this.canvasStyleData.height + 'px',
        transform: `scale(${this.canvasStyleData.scale / 100})`,
        transformOrigin: 'top left',
        position: 'relative',
        margin: '20px auto',
        background: '#fff',
        border: '1px solid #ddd'
      }
    },
    
    selectionBoxStyle() {
      if (!this.curComponent) return {}
      
      const style = this.curComponent.style
      return {
        position: 'absolute',
        top: style.top + 'px',
        left: style.left + 'px',
        width: style.width + 'px',
        height: style.height + 'px',
        border: '2px solid #409eff',
        pointerEvents: 'none',
        zIndex: 9999
      }
    }
  },
  
  methods: {
    handleDrop(e) {
      e.preventDefault()
      const componentData = e.dataTransfer.getData('component')
      if (componentData) {
        const component = JSON.parse(componentData)
        const canvasRect = this.$el.querySelector('.canvas-content').getBoundingClientRect()

        const newComponent = {
          ...component,
          id: generateID(),
          style: {
            ...component.style,
            top: Math.round((e.clientY - canvasRect.top) / (this.canvasStyleData.scale / 100)),
            left: Math.round((e.clientX - canvasRect.left) / (this.canvasStyleData.scale / 100)),
            zIndex: this.componentData.length + 1
          }
        }

        this.$store.commit('addComponent', { component: newComponent })
        this.$store.commit('setCurComponent', {
          component: newComponent,
          index: this.componentData.length
        })
      }
    },
    
    handleDragOver(e) {
      e.preventDefault()
    },
    
    handleClick(component, index) {
      this.$store.commit('setCurComponent', { component, index })
      this.$store.commit('setClickComponentStatus', true)
    },
    
    handleContextMenu(e, component) {
      e.preventDefault()
      // 右键菜单功能
      console.log('Right click on component:', component)
    },

    handleMouseDown(e, component) {
      e.stopPropagation()
      if (component) {
        this.$store.commit('setCurComponent', {
          component,
          index: this.componentData.findIndex(item => item.id === component.id)
        })
      }
    },
    
    deselectCurComponent() {
      this.$store.commit('setCurComponent', { component: null, index: null })
      this.$store.commit('setClickComponentStatus', false)
    },
    
    getShapeStyle(style) {
      return {
        position: 'absolute',
        top: style.top + 'px',
        left: style.left + 'px',
        width: style.width + 'px',
        height: style.height + 'px',
        transform: `rotate(${style.rotate}deg)`,
        opacity: style.opacity,
        zIndex: style.zIndex || 1
      }
    },
    
    getComponentClass(item) {
      return {
        'component-item': true,
        'selected': this.curComponent && this.curComponent.id === item.id
      }
    }
  }
}
</script>

<style scoped>
.editor {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  position: relative;
}

.editor-canvas {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.canvas-content {
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-item {
  cursor: pointer;
  user-select: none;
}

.component-item.selected {
  outline: 2px solid #409eff;
}

.selection-box {
  border-radius: 4px;
}

/* 暗色主题 */
.dark-theme .editor {
  background: #1a1a1a;
}

.dark-theme .canvas-content {
  background: #2d2d2d;
  border-color: #404040;
}
</style>
