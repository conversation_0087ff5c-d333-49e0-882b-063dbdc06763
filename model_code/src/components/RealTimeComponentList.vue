<template>
  <div class="real-time-component-list">
    <div class="list-header">
      <h3>实时组件</h3>
      <el-button size="mini" @click="refreshComponents">
        <i class="el-icon-refresh" />
      </el-button>
    </div>
    
    <div class="component-search">
      <el-input
        v-model="searchText"
        placeholder="搜索组件..."
        size="small"
        prefix-icon="el-icon-search"
        clearable
      />
    </div>
    
    <div class="component-list">
      <div 
        v-for="component in filteredComponents" 
        :key="component.id"
        class="component-item"
        :class="{ active: component.isActive }"
        @click="selectComponent(component)"
        @dragstart="handleDragStart($event, component)"
        draggable
      >
        <div class="component-icon">
          <i :class="component.icon" />
        </div>
        <div class="component-info">
          <div class="component-name">{{ component.name }}</div>
          <div class="component-status">
            <span 
              class="status-dot" 
              :class="component.status"
            />
            {{ getStatusText(component.status) }}
          </div>
        </div>
        <div class="component-actions">
          <el-button 
            size="mini" 
            type="text" 
            @click.stop="editComponent(component)"
          >
            <i class="el-icon-edit" />
          </el-button>
        </div>
      </div>
    </div>
    
    <div v-if="filteredComponents.length === 0" class="empty-state">
      <i class="el-icon-box" />
      <p>暂无实时组件</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RealTimeComponentList',
  data() {
    return {
      searchText: '',
      components: [
        {
          id: 'rt-1',
          name: '实时数据表格',
          icon: 'el-icon-s-grid',
          status: 'online',
          isActive: false,
          type: 'table'
        },
        {
          id: 'rt-2',
          name: '实时图表',
          icon: 'el-icon-s-data',
          status: 'online',
          isActive: false,
          type: 'chart'
        },
        {
          id: 'rt-3',
          name: '状态监控',
          icon: 'el-icon-view',
          status: 'offline',
          isActive: false,
          type: 'monitor'
        }
      ]
    }
  },
  computed: {
    filteredComponents() {
      if (!this.searchText) {
        return this.components
      }
      return this.components.filter(component =>
        component.name.toLowerCase().includes(this.searchText.toLowerCase())
      )
    }
  },
  methods: {
    refreshComponents() {
      // 刷新组件列表
      this.$message.success('组件列表已刷新')
    },
    
    selectComponent(component) {
      // 选中组件
      this.components.forEach(c => c.isActive = false)
      component.isActive = true
      this.$emit('component-selected', component)
    },
    
    editComponent(component) {
      // 编辑组件
      this.$emit('component-edit', component)
    },
    
    handleDragStart(e, component) {
      // 拖拽开始
      e.dataTransfer.setData('componentData', JSON.stringify(component))
    },
    
    getStatusText(status) {
      const statusMap = {
        online: '在线',
        offline: '离线',
        error: '错误'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.real-time-component-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.component-search {
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
}

.component-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.component-item.active {
  border-color: #409eff;
  background: #ecf5ff;
}

.component-icon {
  margin-right: 10px;
  font-size: 20px;
  color: #409eff;
}

.component-info {
  flex: 1;
}

.component-name {
  font-size: 12px;
  color: #303133;
  margin-bottom: 4px;
}

.component-status {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #909399;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.status-dot.online {
  background: #67c23a;
}

.status-dot.offline {
  background: #909399;
}

.status-dot.error {
  background: #f56c6c;
}

.component-actions {
  margin-left: 10px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 10px;
}

/* 暗色主题 */
.dark-theme .real-time-component-list {
  background: #2d2d2d;
}

.dark-theme .list-header {
  border-bottom-color: #404040;
}

.dark-theme .list-header h3 {
  color: #ffffff;
}

.dark-theme .component-search {
  border-bottom-color: #404040;
}

.dark-theme .component-item {
  border-color: #404040;
  background: #3a3a3a;
}

.dark-theme .component-item:hover,
.dark-theme .component-item.active {
  background: #1f2d3d;
}

.dark-theme .component-name {
  color: #ffffff;
}
</style>
