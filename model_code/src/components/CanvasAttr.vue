<template>
  <div class="canvas-attr">
    <div class="attr-header">
      <h4>画布属性</h4>
    </div>
    
    <el-form label-width="80px" size="small">
      <!-- 画布尺寸 -->
      <el-form-item label="画布宽度">
        <el-input-number 
          v-model="canvasStyle.width" 
          :min="100" 
          :max="5000"
          @change="updateCanvasStyle"
        />
        <span style="margin-left: 5px; font-size: 12px;">px</span>
      </el-form-item>
      
      <el-form-item label="画布高度">
        <el-input-number 
          v-model="canvasStyle.height" 
          :min="100" 
          :max="5000"
          @change="updateCanvasStyle"
        />
        <span style="margin-left: 5px; font-size: 12px;">px</span>
      </el-form-item>
      
      <!-- 背景设置 -->
      <el-form-item label="背景颜色">
        <el-color-picker 
          v-model="canvasStyle.backgroundColor" 
          show-alpha
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <el-form-item label="背景图片">
        <el-input 
          v-model="canvasStyle.backgroundImage" 
          placeholder="输入图片URL"
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <el-form-item label="背景重复">
        <el-select 
          v-model="canvasStyle.backgroundRepeat"
          @change="updateCanvasStyle"
        >
          <el-option label="不重复" value="no-repeat" />
          <el-option label="重复" value="repeat" />
          <el-option label="水平重复" value="repeat-x" />
          <el-option label="垂直重复" value="repeat-y" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="背景尺寸">
        <el-select 
          v-model="canvasStyle.backgroundSize"
          @change="updateCanvasStyle"
        >
          <el-option label="自动" value="auto" />
          <el-option label="覆盖" value="cover" />
          <el-option label="包含" value="contain" />
          <el-option label="100%" value="100% 100%" />
        </el-select>
      </el-form-item>
      
      <!-- 网格设置 -->
      <el-form-item label="显示网格">
        <el-switch 
          v-model="canvasStyle.showGrid"
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <el-form-item v-if="canvasStyle.showGrid" label="网格大小">
        <el-input-number 
          v-model="canvasStyle.gridSize" 
          :min="5" 
          :max="100"
          @change="updateCanvasStyle"
        />
        <span style="margin-left: 5px; font-size: 12px;">px</span>
      </el-form-item>
      
      <el-form-item v-if="canvasStyle.showGrid" label="网格颜色">
        <el-color-picker 
          v-model="canvasStyle.gridColor" 
          show-alpha
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <!-- 标尺设置 -->
      <el-form-item label="显示标尺">
        <el-switch 
          v-model="canvasStyle.showRuler"
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <!-- 缩放设置 -->
      <el-form-item label="缩放比例">
        <el-slider
          v-model="canvasStyle.scale"
          :min="10"
          :max="200"
          :step="10"
          show-input
          @change="updateCanvasStyle"
        />
        <span style="margin-left: 5px; font-size: 12px;">%</span>
      </el-form-item>
      
      <!-- 对齐设置 -->
      <el-form-item label="对齐辅助">
        <el-switch 
          v-model="canvasStyle.showAlignLines"
          @change="updateCanvasStyle"
        />
      </el-form-item>
      
      <!-- 画布操作 -->
      <el-form-item label="画布操作">
        <div class="canvas-actions">
          <el-button size="mini" @click="resetCanvas">
            <i class="el-icon-refresh" /> 重置
          </el-button>
          <el-button size="mini" @click="clearCanvas">
            <i class="el-icon-delete" /> 清空
          </el-button>
          <el-button size="mini" @click="exportCanvas">
            <i class="el-icon-download" /> 导出
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'CanvasAttr',
  computed: {
    ...mapState(['canvasStyleData']),
    
    canvasStyle: {
      get() {
        return this.canvasStyleData || this.getDefaultCanvasStyle()
      },
      set(value) {
        this.$store.commit('setCanvasStyle', value)
      }
    }
  },
  methods: {
    getDefaultCanvasStyle() {
      return {
        width: 1200,
        height: 740,
        backgroundColor: '#ffffff',
        backgroundImage: '',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        showGrid: true,
        gridSize: 20,
        gridColor: '#f0f0f0',
        showRuler: true,
        scale: 100,
        showAlignLines: true
      }
    },
    
    updateCanvasStyle() {
      this.$store.commit('setCanvasStyle', this.canvasStyle)
      // 触发画布更新
      this.$emit('canvas-style-change', this.canvasStyle)
    },
    
    resetCanvas() {
      this.$confirm('确定要重置画布设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.canvasStyle = this.getDefaultCanvasStyle()
        this.updateCanvasStyle()
        this.$message.success('画布已重置')
      }).catch(() => {
        // 取消重置
      })
    },
    
    clearCanvas() {
      this.$confirm('确定要清空画布上的所有组件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.commit('setComponentData', [])
        this.$message.success('画布已清空')
      }).catch(() => {
        // 取消清空
      })
    },
    
    exportCanvas() {
      // 导出画布配置
      const canvasData = {
        style: this.canvasStyle,
        components: this.$store.state.componentData
      }
      
      const dataStr = JSON.stringify(canvasData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      
      const link = document.createElement('a')
      link.href = URL.createObjectURL(dataBlob)
      link.download = `canvas-config-${Date.now()}.json`
      link.click()
      
      this.$message.success('画布配置已导出')
    }
  }
}
</script>

<style scoped>
.canvas-attr {
  padding: 15px;
  height: 100%;
  overflow-y: auto;
}

.attr-header {
  margin-bottom: 15px;
}

.attr-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.canvas-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.canvas-actions .el-button {
  flex: 1;
  min-width: 60px;
}

/* 暗色主题 */
.dark-theme .attr-header h4 {
  color: #ffffff;
}
</style>
