<template>
  <div class="event-list">
    <div class="event-header">
      <h4>事件配置</h4>
      <el-button size="mini" type="primary" @click="addEvent">
        <i class="el-icon-plus" /> 添加事件
      </el-button>
    </div>
    
    <div class="event-items">
      <div 
        v-for="(event, index) in events" 
        :key="index"
        class="event-item"
      >
        <div class="event-header-item">
          <span class="event-name">{{ getEventName(event.trigger) }}</span>
          <div class="event-actions">
            <el-button 
              size="mini" 
              type="text" 
              @click="testEvent(event)"
            >
              <i class="el-icon-caret-right" />
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click="deleteEvent(index)"
            >
              <i class="el-icon-delete" />
            </el-button>
          </div>
        </div>
        
        <div class="event-config">
          <el-form size="mini" label-width="60px">
            <el-form-item label="触发器">
              <el-select v-model="event.trigger" size="mini">
                <el-option label="点击" value="click" />
                <el-option label="双击" value="dblclick" />
                <el-option label="鼠标悬停" value="mouseenter" />
                <el-option label="鼠标离开" value="mouseleave" />
                <el-option label="键盘按下" value="keydown" />
                <el-option label="表单提交" value="submit" />
                <el-option label="值改变" value="change" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="动作">
              <el-select v-model="event.action" size="mini">
                <el-option label="显示/隐藏" value="toggle" />
                <el-option label="跳转页面" value="navigate" />
                <el-option label="发送请求" value="request" />
                <el-option label="播放动画" value="animate" />
                <el-option label="弹出提示" value="alert" />
                <el-option label="自定义代码" value="custom" />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="event.action === 'navigate'" label="目标URL">
              <el-input 
                v-model="event.target" 
                placeholder="输入URL或路由路径"
                size="mini"
              />
            </el-form-item>
            
            <el-form-item v-if="event.action === 'request'" label="请求URL">
              <el-input 
                v-model="event.url" 
                placeholder="输入API地址"
                size="mini"
              />
            </el-form-item>
            
            <el-form-item v-if="event.action === 'request'" label="请求方法">
              <el-select v-model="event.method" size="mini">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="event.action === 'alert'" label="提示内容">
              <el-input 
                v-model="event.message" 
                placeholder="输入提示内容"
                size="mini"
              />
            </el-form-item>
            
            <el-form-item v-if="event.action === 'custom'" label="自定义代码">
              <el-input 
                v-model="event.code" 
                type="textarea"
                :rows="3"
                placeholder="输入JavaScript代码"
                size="mini"
              />
            </el-form-item>
            
            <el-form-item label="延迟执行">
              <el-input-number 
                v-model="event.delay" 
                :min="0" 
                :max="5000" 
                :step="100"
                size="mini"
              />
              <span style="margin-left: 5px; font-size: 12px;">毫秒</span>
            </el-form-item>
            
            <el-form-item label="启用">
              <el-switch v-model="event.enabled" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    
    <div v-if="events.length === 0" class="empty-state">
      <i class="el-icon-lightning" />
      <p>暂无事件配置</p>
      <el-button size="small" type="primary" @click="addEvent">
        添加第一个事件
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'EventList',
  computed: {
    ...mapState(['curComponent']),
    
    events: {
      get() {
        return this.curComponent?.events || []
      },
      set(value) {
        if (this.curComponent) {
          this.$set(this.curComponent, 'events', value)
        }
      }
    }
  },
  methods: {
    addEvent() {
      const newEvent = {
        trigger: 'click',
        action: 'alert',
        message: '点击事件触发',
        delay: 0,
        enabled: true
      }
      
      if (!this.curComponent.events) {
        this.$set(this.curComponent, 'events', [])
      }
      
      this.events.push(newEvent)
    },
    
    deleteEvent(index) {
      this.$confirm('确定要删除这个事件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.events.splice(index, 1)
        this.$message.success('事件已删除')
      }).catch(() => {
        // 取消删除
      })
    },
    
    testEvent(event) {
      if (!event.enabled) {
        this.$message.warning('事件已禁用')
        return
      }
      
      this.$message.info(`测试事件: ${this.getEventName(event.trigger)} -> ${this.getActionName(event.action)}`)
      
      // 模拟事件执行
      setTimeout(() => {
        switch (event.action) {
          case 'alert':
            this.$message.success(event.message || '事件执行成功')
            break
          case 'navigate':
            console.log('导航到:', event.target)
            break
          case 'request':
            console.log('发送请求到:', event.url)
            break
          default:
            console.log('执行事件:', event)
        }
      }, event.delay || 0)
    },
    
    getEventName(trigger) {
      const eventMap = {
        click: '点击',
        dblclick: '双击',
        mouseenter: '鼠标悬停',
        mouseleave: '鼠标离开',
        keydown: '键盘按下',
        submit: '表单提交',
        change: '值改变'
      }
      return eventMap[trigger] || trigger
    },
    
    getActionName(action) {
      const actionMap = {
        toggle: '显示/隐藏',
        navigate: '跳转页面',
        request: '发送请求',
        animate: '播放动画',
        alert: '弹出提示',
        custom: '自定义代码'
      }
      return actionMap[action] || action
    }
  }
}
</script>

<style scoped>
.event-list {
  padding: 15px;
  height: 100%;
  overflow-y: auto;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.event-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.event-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.event-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.event-header-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.event-name {
  font-weight: 500;
  color: #303133;
}

.event-actions {
  display: flex;
  gap: 5px;
}

.event-config {
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 10px;
}

/* 暗色主题 */
.dark-theme .event-header h4 {
  color: #ffffff;
}

.dark-theme .event-item {
  border-color: #404040;
  background: #3a3a3a;
}

.dark-theme .event-name {
  color: #ffffff;
}

.dark-theme .event-config {
  border-top-color: #404040;
}
</style>
