<template>
  <div class="component-list">
    <div class="component-list-header">
      <h3>组件库</h3>
    </div>
    <div class="component-list-content">
      <div class="component-category">
        <h4>光伏设备</h4>
        <div class="component-items">
          <div
            v-for="(item, index) in componentList"
            :key="index"
            class="component-item"
            :draggable="true"
            @dragstart="handleDragStart($event, item)"
            @click="addComponent(item)"
          >
            <div class="component-icon">
              <img :src="item.url" :alt="item.label" />
            </div>
            <span class="component-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import componentList from '@/custom-component/component-list'
import { generateID } from '@/utils/generateID'

export default {
  name: 'ComponentList',
  data() {
    return {
      componentList
    }
  },
  computed: {
    ...mapState(['componentData'])
  },
  methods: {
    handleDragStart(e, component) {
      e.dataTransfer.setData('component', JSON.stringify(component))
    },
    
    addComponent(component) {
      const newComponent = this.createComponent(component)
      this.$store.commit('addComponent', { component: newComponent })
      this.$store.commit('setCurComponent', { 
        component: newComponent, 
        index: this.componentData.length 
      })
    },
    
    createComponent(component) {
      return {
        ...component,
        id: generateID(),
        style: {
          ...component.style,
          top: 100,
          left: 100,
          zIndex: 1
        }
      }
    }
  }
}
</script>

<style scoped>
.component-list {
  width: 100%;
  height: 100%;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.component-list-header {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.component-list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.component-list-content {
  padding: 15px;
}

.component-category h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #606266;
}

.component-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.component-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.component-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.component-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.component-label {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 暗色主题 */
.dark-theme .component-list {
  background: #2d2d2d;
  border-right-color: #404040;
}

.dark-theme .component-list-header {
  border-bottom-color: #404040;
}

.dark-theme .component-list-header h3 {
  color: #ffffff;
}

.dark-theme .component-category h4 {
  color: #cccccc;
}

.dark-theme .component-item {
  background: #3a3a3a;
  border-color: #404040;
}

.dark-theme .component-item:hover {
  border-color: #409eff;
}

.dark-theme .component-label {
  color: #cccccc;
}
</style>
