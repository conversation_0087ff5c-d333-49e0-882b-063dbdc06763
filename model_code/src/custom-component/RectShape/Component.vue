<template>
  <div class="rect-shape">
    <v-text
      :prop-value="element.propValue"
      :element="element"
    />
  </div>
</template>

<script>
import VText from '../VText/Component.vue'

export default {
  name: 'RectShape',
  components: {
    VText
  },
  props: {
    element: {
      type: Object,
      required: true
    },
    id: String
  }
}
</script>

<style scoped>
.rect-shape {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>