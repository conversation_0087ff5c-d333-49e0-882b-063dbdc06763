import { render, staticRenderFns } from "./Attr.vue?vue&type=template&id=11b888c8"
import script from "./Attr.vue?vue&type=script&lang=js"
export * from "./Attr.vue?vue&type=script&lang=js"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports