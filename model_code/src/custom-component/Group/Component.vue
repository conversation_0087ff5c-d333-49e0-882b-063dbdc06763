<template>
  <div class="group">
    <div>
      <component
        v-for="item in propValue"
        :key="item.id"
        :is="item.component"
        :id="`component${item.id}`"
        :prop-value="item.propValue"
        :element="item"
        :request="item.request"
        :style="item.groupStyle"
        class="component"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Group',
  props: {
    propValue: {
      type: Array,
      default: () => []
    },
    element: {
      type: Object,
      required: true
    },
    id: String
  }
}
</script>

<style scoped>
.group {
  width: 100%;
  height: 100%;
  position: relative;
}

.component {
  position: absolute;
}
</style>