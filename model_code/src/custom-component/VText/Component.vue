<template>
  <div
    :class="['v-text', { preview: editMode !== 'edit' }]"
    @keydown="handleKeydown"
    @keyup="handleKeyup"
  >
    <!-- 编辑模式 -->
    <div
      v-if="editMode === 'edit'"
      ref="text"
      :class="{ 'can-edit': canEdit }"
      :style="textStyle"
      :contenteditable="canEdit"
      tabindex="0"
      v-html="element.propValue"
      @dblclick="setEdit"
      @paste="clearStyle"
      @mousedown="handleMousedown"
      @blur="handleBlur"
      @input="handleInput"
    />

    <!-- 预览模式 -->
    <div
      v-else
      :style="textStyle"
      v-html="element.propValue"
    />
  </div>
</template>

<script>
export default {
  name: 'VText',
  props: {
    element: {
      type: Object,
      required: true
    },
    propValue: {
      type: String,
      default: '文本'
    },
    editMode: {
      type: String,
      default: 'preview' // 'edit' | 'preview'
    },
    id: String
  },
  data() {
    return {
      canEdit: false,
      isEditing: false
    }
  },
  computed: {
    textStyle() {
      const style = this.element.style || {}
      return {
        verticalAlign: style.verticalAlign || 'top',
        padding: (style.padding || 0) + 'px',
        fontSize: style.fontSize || '14px',
        color: style.color || '#000000',
        fontWeight: style.fontWeight || 'normal',
        textAlign: style.textAlign || 'left',
        lineHeight: style.lineHeight || '1.5',
        wordBreak: 'break-word',
        whiteSpace: 'pre-wrap'
      }
    }
  },
  methods: {
    setEdit() {
      if (this.editMode === 'edit') {
        this.canEdit = true
        this.isEditing = true
        this.$nextTick(() => {
          if (this.$refs.text) {
            this.$refs.text.focus()
            // 选中所有文本
            const range = document.createRange()
            range.selectNodeContents(this.$refs.text)
            const selection = window.getSelection()
            selection.removeAllRanges()
            selection.addRange(range)
          }
        })
      }
    },

    handleInput(e) {
      // 更新组件内容
      const newValue = e.target.innerHTML
      this.$emit('input', newValue)

      // 通过事件通知父组件更新element的propValue
      this.$emit('update-prop-value', newValue)
    },

    handleBlur() {
      this.canEdit = false
      this.isEditing = false
      this.$emit('blur')
    },

    handleKeydown(e) {
      if (this.isEditing) {
        // 阻止某些快捷键在编辑时触发
        if (e.ctrlKey || e.metaKey) {
          e.stopPropagation()
        }

        // Enter键结束编辑
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault()
          this.handleBlur()
        }

        // Escape键取消编辑
        if (e.key === 'Escape') {
          e.preventDefault()
          this.handleBlur()
        }
      }
    },

    handleKeyup(e) {
      if (this.isEditing) {
        e.stopPropagation()
      }
    },

    handleMousedown(e) {
      if (!this.isEditing) {
        e.stopPropagation()
        this.$emit('mousedown', e, this.element)
      }
    },

    clearStyle(e) {
      // 清除粘贴内容的样式
      e.preventDefault()
      try {
        const text = e.clipboardData ? e.clipboardData.getData('text') : ''
        if (text) {
          // 使用现代浏览器的替代方案
          const selection = window.getSelection()
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            range.deleteContents()
            range.insertNode(document.createTextNode(text))
          }
        }
      } catch (error) {
        console.warn('粘贴操作失败:', error)
      }
    }
  },
  mounted() {
    // 如果是编辑模式且没有内容，自动进入编辑状态
    if (this.editMode === 'edit' && !this.element.propValue) {
      this.setEdit()
    }
  }
}
</script>

<style scoped>
.v-text {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: text;
  user-select: text;
}

.v-text.preview {
  cursor: default;
  user-select: none;
}

.can-edit {
  outline: 2px dashed #409eff;
  outline-offset: 2px;
  background-color: rgba(64, 158, 255, 0.1);
}

.can-edit:focus {
  outline: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.2);
}

/* 暗色主题 */
.dark-theme .v-text {
  color: #ffffff;
}

.dark-theme .can-edit {
  outline-color: #66b1ff;
  background-color: rgba(102, 177, 255, 0.1);
}

.dark-theme .can-edit:focus {
  outline-color: #66b1ff;
  background-color: rgba(102, 177, 255, 0.2);
}
</style>