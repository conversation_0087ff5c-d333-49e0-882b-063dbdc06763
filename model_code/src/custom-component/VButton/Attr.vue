<template>
  <div class="attr-list">
    <CommonAttr />
    <el-form label-width="60px" size="small">
      <el-form-item label="按钮文本">
        <el-input
          v-model="curComponent.propValue"
          type="textarea"
          :rows="3"
          placeholder="请输入按钮文本"
        />
      </el-form-item>

      <!-- 按钮类型 -->
      <el-form-item label="按钮类型">
        <el-select v-model="buttonType" placeholder="选择按钮类型">
          <el-option label="默认" value="default" />
          <el-option label="主要" value="primary" />
          <el-option label="成功" value="success" />
          <el-option label="信息" value="info" />
          <el-option label="警告" value="warning" />
          <el-option label="危险" value="danger" />
        </el-select>
      </el-form-item>

      <!-- 按钮尺寸 -->
      <el-form-item label="按钮尺寸">
        <el-select v-model="buttonSize" placeholder="选择按钮尺寸">
          <el-option label="大" value="large" />
          <el-option label="中" value="medium" />
          <el-option label="小" value="small" />
          <el-option label="迷你" value="mini" />
        </el-select>
      </el-form-item>

      <!-- 是否禁用 -->
      <el-form-item label="禁用状态">
        <el-switch v-model="isDisabled" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../common/CommonAttr.vue'

export default {
  name: 'VButtonAttr',
  components: {
    CommonAttr
  },
  computed: {
    ...mapState(['curComponent']),

    buttonType: {
      get() {
        return this.curComponent.buttonType || 'default'
      },
      set(value) {
        this.$set(this.curComponent, 'buttonType', value)
      }
    },

    buttonSize: {
      get() {
        return this.curComponent.buttonSize || 'medium'
      },
      set(value) {
        this.$set(this.curComponent, 'buttonSize', value)
      }
    },

    isDisabled: {
      get() {
        return this.curComponent.disabled || false
      },
      set(value) {
        this.$set(this.curComponent, 'disabled', value)
      }
    }
  }
}
</script>

<style scoped>
.attr-list {
  padding: 10px;
}
</style>