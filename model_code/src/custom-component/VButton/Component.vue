<template>
  <el-button
    :type="element.buttonType || 'default'"
    :size="element.buttonSize || 'medium'"
    :disabled="element.disabled || false"
    class="v-button"
    @click="handleClick"
    @mousedown="handleMouseDown"
  >
    {{ element.propValue || '按钮' }}
  </el-button>
</template>

<script>
export default {
  name: 'VButton',
  props: {
    element: {
      type: Object,
      required: true
    },
    propValue: {
      type: String,
      default: '按钮'
    },
    id: String
  },
  methods: {
    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)

      // 触发自定义事件
      if (this.element.events && this.element.events.click) {
        // 执行点击事件逻辑
        console.log('But<PERSON> clicked:', this.element.label)
      }
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    }
  }
}
</script>

<style scoped>
.v-button {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  cursor: pointer;
  user-select: none;
}

.v-button:focus {
  outline: none;
}

/* 暗色主题 */
.dark-theme .v-button {
  background: #409eff;
  color: white;
}

.dark-theme .v-button:hover {
  background: #66b1ff;
}

.dark-theme .v-button:disabled {
  background: #a0cfff;
  cursor: not-allowed;
}
</style>