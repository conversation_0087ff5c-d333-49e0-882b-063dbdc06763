<template>
  <div class="linkage-container">
    <el-form label-width="80px" size="small">
      <!-- 联动组件列表 -->
      <div
        v-for="(item, index) in linkage.data"
        :key="index"
        class="linkage-component"
      >
        <!-- 删除按钮 -->
        <div class="div-guanbi" @click="deleteLinkageData(index)">
          <span class="iconfont icon-guanbi" />
        </div>

        <!-- 选择联动组件 -->
        <el-form-item label="联动组件">
          <el-select
            v-model="item.id"
            placeholder="请选择联动组件"
            class="testtest"
          >
            <el-option
              v-for="(component, i) in componentData"
              :key="component.id"
              :value="component.id"
              :label="component.label"
            >
              <div
                @mouseenter="onEnter(i)"
                @mouseout="onOut(i)"
              >
                {{ component.label }}
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 选择监听事件 -->
        <el-form-item label="监听事件">
          <el-select
            v-model="item.event"
            placeholder="请选择监听事件"
          >
            <el-option
              v-for="e in eventOptions"
              :key="e.value"
              :value="e.value"
              :label="e.label"
            />
          </el-select>
        </el-form-item>

        <!-- 修改属性说明 -->
        <p class="attr-desc">事件触发时，当前组件要修改的属性</p>

        <!-- 属性配置列表 -->
        <div
          v-for="(e, i) in item.style"
          :key="i"
          class="attr-container"
        >
          <!-- 属性选择 -->
          <el-select
            v-model="e.key"
            @change="e.value = ''"
            placeholder="选择属性"
          >
            <el-option
              v-for="attr in Object.keys(curComponent.style)"
              :key="attr"
              :value="attr"
              :label="styleMap[attr]"
            />
          </el-select>

          <!-- 属性值设置 -->
          <!-- 颜色选择器 -->
          <el-color-picker
            v-if="isIncludesColor(e.key)"
            v-model="e.value"
            show-alpha
          />
          <!-- 下拉选择 -->
          <el-select
            v-else-if="selectKey.includes(e.key)"
            v-model="e.value"
          >
            <el-option
              v-for="option in optionMap[e.key]"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <!-- 数字输入 -->
          <el-input
            v-else
            v-model.number="e.value"
            type="number"
            placeholder="请输入"
          />

          <!-- 删除属性按钮 -->
          <span
            class="iconfont icon-shanchu"
            @click="deleteData(item.style, i)"
          />
        </div>

        <!-- 添加属性按钮 -->
        <el-button @click="addAttr(item.style)">
          添加属性
        </el-button>
      </div>

      <!-- 添加组件按钮 -->
      <el-button
        style="margin-bottom: 10px"
        @click="addComponent"
      >
        添加组件
      </el-button>

      <!-- 过渡时间设置 -->
      <div class="duration-setting">
        <p>过渡时间（秒）</p>
        <el-input
          v-model="linkage.duration"
          class="input-duration"
          placeholder="请输入"
        />
      </div>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'Linkage',
  computed: {
    ...mapState(['curComponent', 'componentData']),

    linkage() {
      return this.curComponent.linkage || { data: [], duration: 0.3 }
    },

    eventOptions() {
      return [
        { label: '点击', value: 'click' },
        { label: '双击', value: 'dblclick' },
        { label: '鼠标悬停', value: 'mouseenter' },
        { label: '鼠标离开', value: 'mouseleave' }
      ]
    },

    styleMap() {
      return {
        width: '宽度',
        height: '高度',
        top: 'Y坐标',
        left: 'X坐标',
        rotate: '旋转',
        opacity: '透明度',
        backgroundColor: '背景色',
        borderWidth: '边框宽度',
        borderColor: '边框颜色',
        borderRadius: '圆角',
        fontSize: '字体大小',
        color: '字体颜色'
      }
    },

    selectKey() {
      return ['borderStyle', 'textAlign', 'fontWeight']
    },

    optionMap() {
      return {
        borderStyle: [
          { label: '实线', value: 'solid' },
          { label: '虚线', value: 'dashed' },
          { label: '点线', value: 'dotted' }
        ],
        textAlign: [
          { label: '左对齐', value: 'left' },
          { label: '居中', value: 'center' },
          { label: '右对齐', value: 'right' }
        ],
        fontWeight: [
          { label: '正常', value: 'normal' },
          { label: '粗体', value: 'bold' }
        ]
      }
    }
  },
  methods: {
    deleteLinkageData(index) {
      this.linkage.data.splice(index, 1)
    },

    onEnter(index) {
      // 鼠标悬停高亮组件
      this.$store.commit('setHoverComponent', this.componentData[index])
    },

    onOut() {
      // 鼠标离开取消高亮
      this.$store.commit('setHoverComponent', null)
    },

    addComponent() {
      if (!this.linkage.data) {
        this.$set(this.linkage, 'data', [])
      }
      this.linkage.data.push({
        id: '',
        event: '',
        style: []
      })
    },

    addAttr(styleList) {
      styleList.push({
        key: '',
        value: ''
      })
    },

    deleteData(styleList, index) {
      styleList.splice(index, 1)
    },

    isIncludesColor(key) {
      return ['color', 'backgroundColor', 'borderColor'].includes(key)
    }
  }
}
</script>

<style scoped>
.linkage-container {
  padding: 10px;
}

.linkage-component {
  position: relative;
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.div-guanbi {
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
  color: #f56c6c;
  font-size: 16px;
  padding: 5px;
}

.div-guanbi:hover {
  color: #f78989;
}

.attr-desc {
  margin: 15px 0 10px 0;
  font-size: 12px;
  color: #606266;
}

.attr-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.attr-container .el-select,
.attr-container .el-input {
  flex: 1;
}

.attr-container .el-color-picker {
  width: 40px;
}

.icon-shanchu {
  cursor: pointer;
  color: #f56c6c;
  font-size: 16px;
  padding: 0 5px;
}

.icon-shanchu:hover {
  color: #f78989;
}

.duration-setting {
  margin-top: 15px;
}

.duration-setting p {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #606266;
}

.input-duration {
  width: 100%;
}

/* 暗色主题 */
.dark-theme .linkage-component {
  background: #3a3a3a;
  border-color: #606266;
}

.dark-theme .attr-desc,
.dark-theme .duration-setting p {
  color: #cccccc;
}
</style>