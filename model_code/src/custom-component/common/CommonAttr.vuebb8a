import { render, staticRenderFns } from "./CommonAttr.vue?vue&type=template&id=2dfa3c9b"
import script from "./CommonAttr.vue?vue&type=script&lang=js"
export * from "./CommonAttr.vue?vue&type=script&lang=js"
import style0 from "./CommonAttr.vue?vue&type=style&index=0&id=2dfa3c9b&prod&lang=scss"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports