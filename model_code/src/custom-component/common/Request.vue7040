import { render, staticRenderFns } from "./Request.vue?vue&type=template&id=5b1a6fc4&scoped=true"
import script from "./Request.vue?vue&type=script&lang=js"
export * from "./Request.vue?vue&type=script&lang=js"
import style0 from "./Request.vue?vue&type=style&index=0&id=5b1a6fc4&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "5b1a6fc4",
  null
  
)

export default component.exports