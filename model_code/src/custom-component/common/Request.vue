<template>
  <div class="request-container">
    <el-form label-width="80px" size="small">
      <!-- 请求地址 -->
      <el-form-item label="请求地址">
        <el-input
          v-model.trim="request.url"
          @blur="validateURL"
          placeholder="请输入API地址"
        >
          <template slot="prepend">HTTPS://</template>
        </el-input>
      </el-form-item>

      <!-- 请求方法 -->
      <el-form-item label="请求方法">
        <el-select v-model="request.method" placeholder="选择请求方法">
          <el-option
            v-for="item in methodOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <!-- 请求参数 -->
      <el-form-item label="请求参数">
        <el-select
          v-model="request.paramType"
          placeholder="参数类型"
          @change="onChange"
        >
          <el-option
            v-for="item in dataOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>

        <!-- 数组类型参数 -->
        <div v-if="request.paramType === 'array'" class="param-container">
          <p>数据项</p>
          <div
            v-for="(item, index) in request.data"
            :key="index"
            class="div-delete"
          >
            <el-input
              v-model="request.data[index]"
              placeholder="请输入参数值"
            />
            <span
              class="iconfont icon-shanchu"
              @click="deleteData(index)"
            />
          </div>
          <el-button @click="addArrayData">添加</el-button>
        </div>

        <!-- 对象/字符串类型参数 -->
        <div
          v-else-if="request.paramType === 'string' || request.paramType === 'object'"
          class="param-container"
        >
          <p>数据项</p>
          <div
            v-for="(item, index) in request.data"
            :key="index"
            class="param-object-container"
          >
            <el-input
              v-model="item[0]"
              placeholder="参数名"
            />
            <el-input
              v-model="item[1]"
              placeholder="参数值"
            />
            <span
              class="iconfont icon-shanchu"
              @click="deleteData(index)"
            />
          </div>
          <el-button @click="addData">添加</el-button>
        </div>
      </el-form-item>

      <!-- 定时触发 -->
      <el-form-item label="定时触发">
        <el-switch v-model="request.series" />
        <template v-if="request.series">
          <p>触发间隔（毫秒）</p>
          <el-input
            v-model="request.time"
            type="number"
            placeholder="请输入间隔时间"
          />
          <p>触发次数（0 为无限）</p>
          <el-input
            v-model="request.requestCount"
            type="number"
            placeholder="请输入触发次数"
          />
        </template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'Request',
  computed: {
    ...mapState(['curComponent']),

    request() {
      return this.curComponent.request || {}
    },

    methodOptions() {
      return ['GET', 'POST', 'PUT', 'DELETE']
    },

    dataOptions() {
      return ['array', 'object', 'string']
    }
  },
  methods: {
    validateURL() {
      // URL验证逻辑
      if (this.request.url && !this.isValidURL(this.request.url)) {
        this.$message.warning('请输入有效的URL地址')
      }
    },

    isValidURL(url) {
      try {
        new URL('https://' + url)
        return true
      } catch {
        return false
      }
    },

    onChange() {
      // 参数类型改变时重置数据
      this.$set(this.request, 'data', [])
    },

    addArrayData() {
      if (!this.request.data) {
        this.$set(this.request, 'data', [])
      }
      this.request.data.push('')
    },

    addData() {
      if (!this.request.data) {
        this.$set(this.request, 'data', [])
      }
      this.request.data.push(['', ''])
    },

    deleteData(index) {
      this.request.data.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.request-container {
  padding: 10px;
}

.param-container {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.param-container p {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #606266;
}

.div-delete {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.div-delete .el-input {
  flex: 1;
  margin-right: 10px;
}

.param-object-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.param-object-container .el-input {
  flex: 1;
  margin-right: 10px;
}

.param-object-container .el-input:first-child {
  max-width: 120px;
}

.icon-shanchu {
  cursor: pointer;
  color: #f56c6c;
  font-size: 16px;
  padding: 0 5px;
}

.icon-shanchu:hover {
  color: #f78989;
}

/* 暗色主题 */
.dark-theme .param-container {
  background: #3a3a3a;
  border-color: #606266;
}

.dark-theme .param-container p {
  color: #cccccc;
}
</style>