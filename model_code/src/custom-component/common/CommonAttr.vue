<template>
  <div class="v-common-attr" @mousedown="setInitial(curComponent.style)">
    <el-collapse v-model="activeName" accordion @change="onChange">
      <!-- 通用样式 -->
      <el-collapse-item title="通用样式" name="style">
        <el-form label-width="70px" size="small">
          <el-form-item
            v-for="({ key, label }, index) in styleKeys"
            :key="index"
            :label="label"
          >
            <!-- 颜色选择器 -->
            <el-color-picker
              v-if="isIncludesColor(key)"
              v-model="curComponent.style[key]"
              show-alpha
            />
            <!-- 下拉选择 -->
            <el-select
              v-else-if="selectKey.includes(key)"
              v-model="curComponent.style[key]"
            >
              <el-option
                v-for="item in optionMap[key]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="numberKeys.includes(key)"
              v-model="curComponent.style[key]"
              :min="getMinValue(key)"
              :max="getMaxValue(key)"
              :step="getStepValue(key)"
              size="small"
            />
            <!-- 文本输入 -->
            <el-input
              v-else
              v-model="curComponent.style[key]"
              size="small"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 设备属性 -->
      <el-collapse-item
        v-if="curComponent.device_attr"
        title="设备属性"
        name="device"
      >
        <el-form label-width="70px" size="small">
          <el-form-item
            v-for="({ key, label }, index) in deviceKeys"
            :key="index"
            :label="label"
          >
            <el-input
              v-model="curComponent.device_attr[key]"
              size="small"
              :placeholder="`请输入${label}`"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>

      <!-- 数据请求 -->
      <el-collapse-item
        v-if="curComponent.request"
        title="数据请求"
        name="request"
      >
        <Request />
      </el-collapse-item>

      <!-- 组件联动 -->
      <el-collapse-item
        v-if="curComponent.linkage"
        title="组件联动"
        name="linkage"
      >
        <Linkage />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Request from './Request.vue'
import Linkage from './Linkage.vue'

export default {
  name: 'CommonAttr',
  components: {
    Request,
    Linkage
  },
  data() {
    return {
      activeName: 'style'
    }
  },
  computed: {
    ...mapState(['curComponent']),

    // 样式属性配置
    styleKeys() {
      const baseKeys = [
        { key: 'width', label: '宽度' },
        { key: 'height', label: '高度' },
        { key: 'top', label: 'Y坐标' },
        { key: 'left', label: 'X坐标' },
        { key: 'rotate', label: '旋转' },
        { key: 'opacity', label: '透明度' },
        { key: 'borderWidth', label: '边框宽度' },
        { key: 'borderColor', label: '边框颜色' },
        { key: 'borderStyle', label: '边框样式' },
        { key: 'borderRadius', label: '圆角' },
        { key: 'backgroundColor', label: '背景色' }
      ]

      // 根据组件类型添加特定属性
      if (this.curComponent.component === 'VText') {
        baseKeys.push(
          { key: 'fontSize', label: '字体大小' },
          { key: 'fontWeight', label: '字体粗细' },
          { key: 'color', label: '字体颜色' },
          { key: 'textAlign', label: '对齐方式' }
        )
      }

      return baseKeys
    },

    // 设备属性配置
    deviceKeys() {
      if (!this.curComponent.device_attr) return []

      return [
        { key: 'sn', label: '设备编号' },
        { key: 'code', label: '设备代码' }
      ].filter(item => this.curComponent.device_attr.hasOwnProperty(item.key))
    },

    // 颜色属性
    colorKeys() {
      return ['color', 'backgroundColor', 'borderColor']
    },

    // 下拉选择属性
    selectKey() {
      return ['borderStyle', 'textAlign', 'fontWeight']
    },

    // 数字输入属性
    numberKeys() {
      return ['width', 'height', 'top', 'left', 'rotate', 'opacity', 'borderWidth', 'borderRadius', 'fontSize']
    },

    // 下拉选项映射
    optionMap() {
      return {
        borderStyle: [
          { label: '实线', value: 'solid' },
          { label: '虚线', value: 'dashed' },
          { label: '点线', value: 'dotted' },
          { label: '无边框', value: 'none' }
        ],
        textAlign: [
          { label: '左对齐', value: 'left' },
          { label: '居中', value: 'center' },
          { label: '右对齐', value: 'right' }
        ],
        fontWeight: [
          { label: '正常', value: 'normal' },
          { label: '粗体', value: 'bold' },
          { label: '更粗', value: 'bolder' },
          { label: '更细', value: 'lighter' }
        ]
      }
    }
  },
  methods: {
    onChange(activeName) {
      // 记录当前打开的面板
      if (this.curComponent) {
        this.curComponent.collapseName = activeName
      }
    },

    setInitial(style) {
      // 设置初始样式值
      if (!style) return

      // 确保必要的样式属性存在
      const defaultStyle = {
        borderWidth: 0,
        borderColor: '#000000',
        borderStyle: 'solid',
        borderRadius: 0,
        opacity: 1
      }

      Object.keys(defaultStyle).forEach(key => {
        if (style[key] === undefined) {
          this.$set(style, key, defaultStyle[key])
        }
      })
    },

    isIncludesColor(key) {
      return this.colorKeys.includes(key)
    },

    getMinValue(key) {
      const minMap = {
        width: 1,
        height: 1,
        top: 0,
        left: 0,
        rotate: 0,
        opacity: 0,
        borderWidth: 0,
        borderRadius: 0,
        fontSize: 12
      }
      return minMap[key] || 0
    },

    getMaxValue(key) {
      const maxMap = {
        rotate: 360,
        opacity: 1,
        borderWidth: 20,
        borderRadius: 50,
        fontSize: 100
      }
      return maxMap[key] || 9999
    },

    getStepValue(key) {
      const stepMap = {
        opacity: 0.1,
        rotate: 1
      }
      return stepMap[key] || 1
    }
  },

  mounted() {
    // 恢复上次打开的面板
    if (this.curComponent && this.curComponent.collapseName) {
      this.activeName = this.curComponent.collapseName
    }
  }
}
</script>

<style scoped>
.v-common-attr {
  padding: 10px;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

/* 暗色主题 */
.dark-theme .v-common-attr {
  background: #2d2d2d;
}
</style>