import { render, staticRenderFns } from "./Linkage.vue?vue&type=template&id=6f5b272d&scoped=true"
import script from "./Linkage.vue?vue&type=script&lang=js"
export * from "./Linkage.vue?vue&type=script&lang=js"
import style0 from "./Linkage.vue?vue&type=style&index=0&id=6f5b272d&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "6f5b272d",
  null
  
)

export default component.exports