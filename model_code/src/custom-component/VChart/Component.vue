<template>
  <div class="v-chart-container">
    <v-chart
      ref="chart"
      class="chart"
      :option="chartOption"
      autoresize
      @click="handleClick"
      @mousedown="handleMouseDown"
    />
  </div>
</template>

<script>
import VChart from 'vue-echarts'
import 'echarts'

export default {
  name: 'VChart',
  components: {
    VChart
  },
  props: {
    element: {
      type: Object,
      required: true
    },
    propValue: {
      type: Object,
      default: () => ({})
    },
    id: String
  },
  computed: {
    chartOption() {
      // 获取图表配置，优先使用element.option，其次使用propValue.option
      const option = this.element.option || this.propValue.option || this.getDefaultOption()

      // 确保配置的完整性
      return {
        title: {
          text: '图表标题',
          show: true,
          ...option.title
        },
        tooltip: {
          trigger: 'axis',
          show: true,
          ...option.tooltip
        },
        legend: {
          show: true,
          ...option.legend
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: true,
          ...option.xAxis
        },
        yAxis: {
          type: 'value',
          show: true,
          ...option.yAxis
        },
        series: option.series || [{
          data: [120, 200, 150, 80, 70, 110, 130],
          type: 'bar'
        }],
        ...option
      }
    }
  },
  methods: {
    getDefaultOption() {
      return {
        title: {
          text: '示例图表',
          show: true
        },
        tooltip: {
          trigger: 'axis',
          show: true
        },
        legend: {
          show: true
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: true
        },
        yAxis: {
          type: 'value',
          show: true
        },
        series: [{
          data: [120, 200, 150, 80, 70, 110, 130],
          type: 'bar'
        }]
      }
    },

    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    },

    // 刷新图表
    refreshChart() {
      if (this.$refs.chart) {
        this.$refs.chart.resize()
      }
    }
  },
  mounted() {
    // 组件挂载后刷新图表
    this.$nextTick(() => {
      this.refreshChart()
    })
  }
}
</script>

<style scoped>
.v-chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 暗色主题 */
.dark-theme .v-chart-container {
  background: #2d2d2d;
}
</style>