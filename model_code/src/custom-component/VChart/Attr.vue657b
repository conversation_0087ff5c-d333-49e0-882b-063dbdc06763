import { render, staticRenderFns } from "./Attr.vue?vue&type=template&id=02c0ddca"
import script from "./Attr.vue?vue&type=script&lang=js"
export * from "./Attr.vue?vue&type=script&lang=js"
import style0 from "./Attr.vue?vue&type=style&index=0&id=02c0ddca&prod&lang=css"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports