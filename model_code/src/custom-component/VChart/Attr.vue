<template>
  <div class="attr">
    <CommonAttr />

    <el-form label-width="80px" size="small">
      <!-- 标题设置 -->
      <el-form-item label="标题">
        <el-switch
          v-model="option.title.show"
          active-text="显示标题"
        />
        <el-input
          v-model="option.title.text"
          placeholder="请输入标题内容"
          style="margin-top: 10px;"
        />
      </el-form-item>

      <!-- 工具提示 -->
      <el-form-item label="工具提示">
        <el-switch
          v-model="option.tooltip.show"
          active-text="显示提示"
        />
      </el-form-item>

      <!-- 图例 -->
      <el-form-item label="图例">
        <el-switch
          v-model="option.legend.show"
          active-text="显示图例"
        />
      </el-form-item>

      <!-- 横坐标 -->
      <el-form-item label="横坐标">
        <el-switch
          v-model="option.xAxis.show"
          active-text="显示横坐标"
        />
      </el-form-item>

      <!-- 图表类型选择 -->
      <el-form-item label="图表类型">
        <el-dropdown @command="selectChart">
          <span class="el-dropdown-link">
            更换图表类型
            <i class="el-icon-arrow-down el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(chart, index) in charts"
              :key="index"
              :command="chart.title"
            >
              {{ chart.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>

      <!-- 静态数据源 -->
      <el-form-item label="静态数据源">
        <el-button @click="openStaticWinbox">
          修改数据
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据修改对话框 -->
    <el-dialog
      title="数据修改"
      :visible.sync="dialogVisible"
      width="75%"
    >
      <div ref="ace" class="ace" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateData">
          更新数据
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../common/CommonAttr.vue'

export default {
  name: 'VChartAttr',
  components: {
    CommonAttr
  },
  data() {
    return {
      dialogVisible: false,
      charts: [
        { name: '柱状图', title: 'bar' },
        { name: '折线图', title: 'line' },
        { name: '饼图', title: 'pie' },
        { name: '散点图', title: 'scatter' },
        { name: '雷达图', title: 'radar' }
      ]
    }
  },
  computed: {
    ...mapState(['curComponent']),

    option() {
      return this.curComponent.option || {
        title: { show: true, text: '图表标题' },
        tooltip: { show: true },
        legend: { show: true },
        xAxis: { show: true }
      }
    }
  },
  methods: {
    selectChart(chartType) {
      // 更换图表类型
      this.$set(this.curComponent, 'chartType', chartType)
      this.updateChartOption(chartType)
    },

    updateChartOption(type) {
      // 根据图表类型更新配置
      const baseOption = {
        title: this.option.title,
        tooltip: this.option.tooltip,
        legend: this.option.legend,
        xAxis: this.option.xAxis
      }

      // 根据不同图表类型设置不同的默认配置
      switch (type) {
        case 'bar':
          baseOption.series = [{ type: 'bar', data: [10, 20, 30, 40, 50] }]
          break
        case 'line':
          baseOption.series = [{ type: 'line', data: [10, 20, 30, 40, 50] }]
          break
        case 'pie':
          baseOption.series = [{
            type: 'pie',
            data: [
              { value: 335, name: '直接访问' },
              { value: 310, name: '邮件营销' },
              { value: 234, name: '联盟广告' }
            ]
          }]
          break
        default:
          baseOption.series = [{ type: 'bar', data: [10, 20, 30, 40, 50] }]
      }

      this.$set(this.curComponent, 'option', baseOption)
    },

    openStaticWinbox() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.initAceEditor()
      })
    },

    initAceEditor() {
      // 初始化代码编辑器 (如果有ace编辑器的话)
      if (this.$refs.ace) {
        this.$refs.ace.innerHTML = JSON.stringify(this.option, null, 2)
      }
    },

    updateData() {
      try {
        const newData = JSON.parse(this.$refs.ace.textContent || '{}')
        this.$set(this.curComponent, 'option', newData)
        this.dialogVisible = false
        this.$message.success('数据更新成功')
      } catch (error) {
        this.$message.error('数据格式错误')
      }
    }
  }
}
</script>

<style scoped>
.attr {
  padding: 10px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

.ace {
  width: 100%;
  height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  overflow: auto;
  background: #f5f5f5;
}

.dialog-footer {
  text-align: right;
}

/* 暗色主题 */
.dark-theme .ace {
  background: #2d2d2d;
  color: #ffffff;
  border-color: #404040;
}
</style>