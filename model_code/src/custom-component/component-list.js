// 公共样式
export const commonStyle = {
    rotate: 0,
    opacity: 1,
}

export const commonAttr = {
    animations: [],
    events: {},
    groupStyle: {}, // 当一个组件成为 Group 的子组件时使用
    isLock: false, // 是否锁定组件
    collapseName: 'style', // 编辑组件时记录当前使用的是哪个折叠面板，再次回来时恢复上次打开的折叠面板，优化用户体验
    linkage: {
        duration: 0, // 过渡持续时间
        data: [
            // 组件联动
            {
                id: '', // 联动的组件 id
                label: '', // 联动的组件名称
                event: '', // 监听事件
                style: [{
                    key: '',
                    value: ''
                }], // 监听的事件触发时，需要改变的属性
            },
        ],
    },
}

// 编辑器左侧组件列表
const list = [{
        component: 'Picture',
        label: '逆变器',
        icon: 'tupian',
        device_type: 2,
        url: require('@/assets/bzq.png'),
        propValue: {
            url: require('@/assets/bzq.png'),
            flip: {
                horizontal: false,
                vertical: false,
            },
        },
        style: {
            width: 90,
            height: 90,
            borderRadius: '',
        },
    },

    {
        component: 'Picture',
        label: '采集器',
        icon: 'tupian',
        device_type: 3,
        url: require('@/assets/cjq.png'),
        propValue: {
            url: require('@/assets/cjq.png'),
            flip: {
                horizontal: false,
                vertical: false,
            },
        },
        device_attr: {
            // code: '',
            sn: '',
        },
        style: {
            width: 80,
            height: 100,
            borderRadius: '',
        },
    },

    {
        component: 'Picture',
        label: '光伏组件',
        icon: 'tupian',
        device_type: 5,
        url: require('@/assets/gfzj.png'),
        propValue: {
            url: require('@/assets/gfzj.png'),
            flip: {
                horizontal: false,
                vertical: false,
            },
        },
        device_attr: {
            // code: '',
            sn: '',
        },
        style: {
            width: 70,
            height: 80,
            borderRadius: '',
        },
    },
    {
        component: 'Picture',
        label: '光伏组件',
        icon: 'tupian',
        device_type: 4,
        url: require('@/assets/device_moudel/80-100.png'),
        propValue: {
            url: require('@/assets/device_moudel/80-100.png'),
            flip: {
                horizontal: false,
                vertical: false,
            },
        },
        device_attr: {
            code: '',
            sn: '',
        },
        style: {
            width: 70,
            height: 80,
            borderRadius: '',
        },
    },
]

for (let i = 0, len = list.length; i < len; i++) {
    const item = list[i]
    item.style = {
        ...commonStyle,
        ...item.style
    }
    list[i] = {
        ...commonAttr,
        ...item
    }
}

export default list