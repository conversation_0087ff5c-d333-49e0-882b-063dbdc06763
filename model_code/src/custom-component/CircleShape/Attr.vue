<template>
  <div class="attr-list">
    <CommonAttr />
    <el-form label-width="60px" size="small">
      <el-form-item label="内容">
        <el-input
          v-model="curComponent.propValue"
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../common/CommonAttr.vue'

export default {
  name: 'CircleShapeAttr',
  components: {
    CommonAttr
  },
  computed: {
    ...mapState(['curComponent'])
  }
}
</script>

<style scoped>
.attr-list {
  padding: 10px;
}
</style>