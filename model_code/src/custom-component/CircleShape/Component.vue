<template>
  <div class="circle-shape">
    <v-text
      :prop-value="element.propValue"
      :element="element"
    />
  </div>
</template>

<script>
import VText from '../VText/Component.vue'

export default {
  name: 'CircleShape',
  components: {
    VText
  },
  props: {
    element: {
      type: Object,
      required: true
    },
    id: String
  }
}
</script>

<style scoped>
.circle-shape {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>