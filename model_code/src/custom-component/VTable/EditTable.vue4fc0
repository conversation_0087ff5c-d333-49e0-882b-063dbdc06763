import { render, staticRenderFns } from "./EditTable.vue?vue&type=template&id=5f873fa8&scoped=true"
import script from "./EditTable.vue?vue&type=script&lang=js"
export * from "./EditTable.vue?vue&type=script&lang=js"
import style0 from "./EditTable.vue?vue&type=style&index=0&id=5f873fa8&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "5f873fa8",
  null
  
)

export default component.exports