<template>
  <div class="edit-table">
    <div class="table-header">
      <h4>表格数据编辑</h4>
    </div>

    <!-- 表格编辑区域 -->
    <div class="table-container">
      <table class="editable-table" @dblclick="onDblclick">
        <tbody>
          <tr
            v-for="(item, row) in tableData"
            :key="row"
            :class="{ 'header-row': row === 0 }"
          >
            <td
              v-for="(cell, col) in item"
              :key="col"
              :class="{
                selected: curTd === `${row},${col}`,
                'header-cell': row === 0
              }"
              @click="onClick(row, col)"
            >
              <!-- 编辑模式 -->
              <el-input
                v-if="canEdit && curTd === `${row},${col}`"
                v-model="tableData[row][col]"
                v-focus
                size="mini"
                @blur="onBlur"
                @keyup.enter="onBlur"
              />
              <!-- 显示模式 -->
              <span v-else>{{ cell }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 操作按钮 -->
    <div class="table-actions">
      <el-button size="mini" @click="addRow">
        <i class="el-icon-plus" /> 添加行
      </el-button>
      <el-button size="mini" @click="addCol">
        <i class="el-icon-plus" /> 添加列
      </el-button>
      <el-button size="mini" type="danger" @click="deleteRow">
        <i class="el-icon-minus" /> 删除行
      </el-button>
      <el-button size="mini" type="danger" @click="deleteCol">
        <i class="el-icon-minus" /> 删除列
      </el-button>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-button size="mini" type="primary" @click="resetTable">
        重置表格
      </el-button>
      <el-button size="mini" @click="importData">
        导入数据
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'EditTable',
  directives: {
    focus: {
      inserted(el) {
        // 聚焦到input元素
        const input = el.querySelector('input')
        if (input) {
          input.focus()
          input.select()
        }
      }
    }
  },
  data() {
    return {
      canEdit: false,
      curTd: '',
      selectedRow: -1,
      selectedCol: -1
    }
  },
  computed: {
    ...mapState(['curComponent']),

    tableData: {
      get() {
        return this.curComponent.propValue?.data || [
          ['列1', '列2', '列3'],
          ['数据1', '数据2', '数据3'],
          ['数据4', '数据5', '数据6']
        ]
      },
      set(value) {
        if (!this.curComponent.propValue) {
          this.$set(this.curComponent, 'propValue', {})
        }
        this.$set(this.curComponent.propValue, 'data', value)
      }
    }
  },
  methods: {
    onClick(row, col) {
      this.selectedRow = row
      this.selectedCol = col
      this.curTd = `${row},${col}`
      this.canEdit = false
    },

    onDblclick() {
      if (this.curTd) {
        this.canEdit = true
      }
    },

    onBlur() {
      this.canEdit = false
    },

    addRow() {
      const colCount = this.tableData[0]?.length || 3
      const newRow = new Array(colCount).fill('新数据')
      this.tableData.push(newRow)
    },

    addCol() {
      this.tableData.forEach((row, index) => {
        if (index === 0) {
          row.push('新列')
        } else {
          row.push('新数据')
        }
      })
    },

    deleteRow() {
      if (this.selectedRow >= 0 && this.tableData.length > 1) {
        this.tableData.splice(this.selectedRow, 1)
        this.curTd = ''
        this.selectedRow = -1
      }
    },

    deleteCol() {
      if (this.selectedCol >= 0 && this.tableData[0]?.length > 1) {
        this.tableData.forEach(row => {
          row.splice(this.selectedCol, 1)
        })
        this.curTd = ''
        this.selectedCol = -1
      }
    },

    resetTable() {
      this.tableData = [
        ['列1', '列2', '列3'],
        ['数据1', '数据2', '数据3'],
        ['数据4', '数据5', '数据6']
      ]
      this.curTd = ''
      this.selectedRow = -1
      this.selectedCol = -1
    },

    importData() {
      // 这里可以实现数据导入功能
      this.$message.info('数据导入功能待实现')
    }
  }
}
</script>

<style scoped>
.edit-table {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 15px;
}

.table-header {
  margin-bottom: 10px;
}

.table-header h4 {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.table-container {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
}

.editable-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.editable-table td {
  border: 1px solid #e4e7ed;
  padding: 8px;
  min-width: 80px;
  cursor: pointer;
  position: relative;
}

.editable-table td.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.editable-table td.header-cell {
  background-color: #f5f7fa;
  font-weight: bold;
}

.editable-table tr.header-row td {
  background-color: #f5f7fa;
  font-weight: bold;
}

.table-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

/* 暗色主题 */
.dark-theme .edit-table {
  border-color: #606266;
  background: #2d2d2d;
}

.dark-theme .table-header h4 {
  color: #cccccc;
}

.dark-theme .table-container {
  border-color: #606266;
}

.dark-theme .editable-table td {
  border-color: #606266;
  background: #2d2d2d;
  color: #ffffff;
}

.dark-theme .editable-table td.selected {
  background-color: #1f2d3d;
  border-color: #409eff;
}

.dark-theme .editable-table td.header-cell,
.dark-theme .editable-table tr.header-row td {
  background-color: #404040;
}
</style>