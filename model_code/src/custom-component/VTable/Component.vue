<template>
  <div class="v-table-container">
    <el-table
      :data="tableData"
      :stripe="propValue.stripe"
      :border="propValue.border"
      :size="propValue.size || 'medium'"
      :height="propValue.height"
      style="width: 100%"
      @click="handleClick"
      @mousedown="handleMouseDown"
    >
      <el-table-column
        v-for="(column, index) in columns"
        :key="index"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align || 'left'"
      >
        <template slot-scope="scope">
          <span :style="getCellStyle(scope.$index, index)">
            {{ scope.row[column.prop] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'VTable',
  props: {
    element: {
      type: Object,
      required: true
    },
    propValue: {
      type: Object,
      default: () => ({
        data: [],
        stripe: false,
        thBold: true,
        border: true,
        size: 'medium',
        height: null
      })
    },
    id: String
  },
  computed: {
    tableData() {
      // 处理表格数据，跳过表头行
      const data = this.propValue.data || []
      if (data.length <= 1) return []

      const headers = data[0] || []
      const rows = data.slice(1)

      return rows.map(row => {
        const rowData = {}
        headers.forEach((header, index) => {
          rowData[`col_${index}`] = row[index] || ''
        })
        return rowData
      })
    },

    columns() {
      // 生成表格列配置
      const data = this.propValue.data || []
      if (data.length === 0) return []

      const headers = data[0] || []
      return headers.map((header, index) => ({
        prop: `col_${index}`,
        label: header || `列${index + 1}`,
        width: this.getColumnWidth(index),
        minWidth: 80,
        align: 'center'
      }))
    }
  },
  methods: {
    getColumnWidth(index) {
      // 根据内容自动计算列宽
      const data = this.propValue.data || []
      let maxLength = 0

      data.forEach(row => {
        if (row[index]) {
          maxLength = Math.max(maxLength, String(row[index]).length)
        }
      })

      return Math.max(80, Math.min(200, maxLength * 12 + 40))
    },

    getCellStyle(rowIndex) {
      const style = {}

      // 表头加粗
      if (this.propValue.thBold && rowIndex === 0) {
        style.fontWeight = 'bold'
      }

      return style
    },

    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    }
  }
}
</script>

<style scoped>
.v-table-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* 自定义表格样式 */
.v-table-container ::v-deep .el-table {
  font-size: 12px;
}

.v-table-container ::v-deep .el-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.v-table-container ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

/* 暗色主题 */
.dark-theme .v-table-container ::v-deep .el-table {
  background-color: #2d2d2d;
  color: #ffffff;
}

.dark-theme .v-table-container ::v-deep .el-table th {
  background-color: #404040;
  color: #ffffff;
}

.dark-theme .v-table-container ::v-deep .el-table td {
  border-color: #606266;
}

.dark-theme .v-table-container ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #3a3a3a;
}
</style>