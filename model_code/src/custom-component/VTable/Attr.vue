<template>
  <div class="attr-list v-table-attr">
    <CommonAttr />
    <EditTable />

    <el-form label-width="80px" size="small">
      <!-- 斑马纹设置 -->
      <el-form-item label="斑马纹">
        <el-switch v-model="propValue.stripe" />
      </el-form-item>

      <!-- 表头加粗 -->
      <el-form-item label="表头加粗">
        <el-switch v-model="propValue.thBold" />
      </el-form-item>

      <!-- 边框设置 -->
      <el-form-item label="显示边框">
        <el-switch v-model="propValue.border" />
      </el-form-item>

      <!-- 表格尺寸 -->
      <el-form-item label="表格尺寸">
        <el-select v-model="propValue.size" placeholder="选择尺寸">
          <el-option label="大" value="large" />
          <el-option label="中" value="medium" />
          <el-option label="小" value="small" />
          <el-option label="迷你" value="mini" />
        </el-select>
      </el-form-item>

      <!-- 高度设置 -->
      <el-form-item label="固定高度">
        <el-switch v-model="enableFixedHeight" />
        <el-input-number
          v-if="enableFixedHeight"
          v-model="propValue.height"
          :min="100"
          :max="1000"
          style="margin-top: 10px; width: 100%;"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../common/CommonAttr.vue'
import EditTable from './EditTable.vue'

export default {
  name: 'VTableAttr',
  components: {
    CommonAttr,
    EditTable
  },
  computed: {
    ...mapState(['curComponent']),

    propValue() {
      return this.curComponent.propValue || {
        stripe: false,
        thBold: true,
        border: true,
        size: 'medium',
        height: null
      }
    },

    enableFixedHeight: {
      get() {
        return this.propValue.height !== null && this.propValue.height !== undefined
      },
      set(value) {
        if (value) {
          this.$set(this.propValue, 'height', 300)
        } else {
          this.$set(this.propValue, 'height', null)
        }
      }
    }
  }
}
</script>

<style scoped>
.v-table-attr {
  padding: 10px;
}

.attr-list {
  padding: 10px;
}
</style>