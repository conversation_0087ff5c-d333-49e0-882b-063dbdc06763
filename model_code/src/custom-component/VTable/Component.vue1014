import { render, staticRenderFns } from "./Component.vue?vue&type=template&id=28f6189e&scoped=true"
import script from "./Component.vue?vue&type=script&lang=js"
export * from "./Component.vue?vue&type=script&lang=js"
import style0 from "./Component.vue?vue&type=style&index=0&id=28f6189e&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "28f6189e",
  null
  
)

export default component.exports