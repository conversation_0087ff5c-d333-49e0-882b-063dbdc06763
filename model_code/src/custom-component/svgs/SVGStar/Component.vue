<template>
  <div class="svg-star-container">
    <!-- SVG星形 -->
    <svg
      version="1.1"
      baseProfile="full"
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 100 100"
      preserveAspectRatio="none"
    >
      <polygon
        ref="star"
        :points="points"
        :stroke="strokeColor"
        :fill="fillColor"
        :stroke-width="strokeWidth"
      />
    </svg>

    <!-- 文本内容 -->
    <div class="text-overlay">
      <v-text
        :prop-value="element.propValue"
        :element="element"
        :edit-mode="editMode"
      />
    </div>
  </div>
</template>

<script>
import VText from '../../VText/Component.vue'

export default {
  name: 'SVGStar',
  components: {
    VText
  },
  props: {
    element: {
      type: Object,
      required: true
    },
    editMode: {
      type: String,
      default: 'preview'
    },
    id: String
  },
  computed: {
    points() {
      // 生成星形的顶点坐标
      const numPoints = this.element.svgStyle?.points || 5
      const outerRadius = 45
      const innerRadius = 20
      const centerX = 50
      const centerY = 50

      let points = []

      for (let i = 0; i < numPoints * 2; i++) {
        const angle = (i * Math.PI) / numPoints - Math.PI / 2
        const radius = i % 2 === 0 ? outerRadius : innerRadius
        const x = centerX + radius * Math.cos(angle)
        const y = centerY + radius * Math.sin(angle)
        points.push(`${x.toFixed(2)},${y.toFixed(2)}`)
      }

      return points.join(' ')
    },

    fillColor() {
      return this.element.svgStyle?.fill ||
             this.element.style?.backgroundColor ||
             '#ffd700'
    },

    strokeColor() {
      return this.element.svgStyle?.stroke ||
             this.element.style?.borderColor ||
             '#000000'
    },

    strokeWidth() {
      return this.element.svgStyle?.strokeWidth ||
             this.element.style?.borderWidth ||
             1
    }
  },
  methods: {
    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    }
  }
}
</script>

<style scoped>
.svg-star-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.svg-star-container svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.text-overlay {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-overlay .v-text {
  pointer-events: auto;
  max-width: 60%;
  text-align: center;
}

/* 暗色主题 */
.dark-theme .svg-star-container {
  background: transparent;
}
</style>