<template>
  <div class="attr-list">
    <CommonAttr />
    <el-form label-width="60px" size="small">
      <el-form-item label="内容">
        <el-input
          v-model="curComponent.propValue"
          type="textarea"
          :rows="3"
          placeholder="请输入星形内容"
        />
      </el-form-item>

      <!-- 星形特有属性 -->
      <el-form-item label="星形角数">
        <el-input-number
          v-model="starPoints"
          :min="3"
          :max="12"
          size="small"
        />
      </el-form-item>

      <el-form-item label="填充颜色">
        <el-color-picker
          v-model="fillColor"
          show-alpha
        />
      </el-form-item>

      <el-form-item label="边框颜色">
        <el-color-picker
          v-model="strokeColor"
          show-alpha
        />
      </el-form-item>

      <el-form-item label="边框宽度">
        <el-input-number
          v-model="strokeWidth"
          :min="0"
          :max="10"
          size="small"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../../common/CommonAttr.vue'

export default {
  name: 'SVGStarAttr',
  components: {
    CommonAttr
  },
  computed: {
    ...mapState(['curComponent']),

    starPoints: {
      get() {
        return this.curComponent.svgStyle?.points || 5
      },
      set(value) {
        this.updateSvgStyle('points', value)
      }
    },

    fillColor: {
      get() {
        return this.curComponent.svgStyle?.fill || '#ffd700'
      },
      set(value) {
        this.updateSvgStyle('fill', value)
      }
    },

    strokeColor: {
      get() {
        return this.curComponent.svgStyle?.stroke || '#000000'
      },
      set(value) {
        this.updateSvgStyle('stroke', value)
      }
    },

    strokeWidth: {
      get() {
        return this.curComponent.svgStyle?.strokeWidth || 1
      },
      set(value) {
        this.updateSvgStyle('strokeWidth', value)
      }
    }
  },
  methods: {
    updateSvgStyle(key, value) {
      if (!this.curComponent.svgStyle) {
        this.$set(this.curComponent, 'svgStyle', {})
      }
      this.$set(this.curComponent.svgStyle, key, value)
    }
  }
}
</script>

<style scoped>
.attr-list {
  padding: 10px;
}
</style>