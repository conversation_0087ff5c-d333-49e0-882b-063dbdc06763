import { render, staticRenderFns } from "./Component.vue?vue&type=template&id=1d0c747f&scoped=true"
import script from "./Component.vue?vue&type=script&lang=js"
export * from "./Component.vue?vue&type=script&lang=js"
import style0 from "./Component.vue?vue&type=style&index=0&id=1d0c747f&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "1d0c747f",
  null
  
)

export default component.exports