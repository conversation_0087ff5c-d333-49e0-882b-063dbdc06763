<template>
  <div class="svg-triangle-container">
    <!-- SVG三角形 -->
    <svg
      version="1.1"
      baseProfile="full"
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
      viewBox="0 0 100 100"
      preserveAspectRatio="none"
    >
      <polygon
        ref="triangle"
        :points="points"
        :stroke="strokeColor"
        :fill="fillColor"
        :stroke-width="strokeWidth"
      />
    </svg>

    <!-- 文本内容 -->
    <div class="text-overlay">
      <v-text
        :prop-value="element.propValue"
        :element="element"
        :edit-mode="editMode"
      />
    </div>
  </div>
</template>

<script>
import VText from '../../VText/Component.vue'

export default {
  name: 'SVGTriangle',
  components: {
    VText
  },
  props: {
    element: {
      type: Object,
      required: true
    },
    editMode: {
      type: String,
      default: 'preview'
    },
    id: String
  },
  computed: {
    points() {
      // 生成三角形的三个顶点坐标
      return '50,10 90,90 10,90'
    },

    fillColor() {
      return this.element.svgStyle?.fill ||
             this.element.style?.backgroundColor ||
             '#409eff'
    },

    strokeColor() {
      return this.element.svgStyle?.stroke ||
             this.element.style?.borderColor ||
             '#000000'
    },

    strokeWidth() {
      return this.element.svgStyle?.strokeWidth ||
             this.element.style?.borderWidth ||
             1
    }
  },
  methods: {
    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    }
  }
}
</script>

<style scoped>
.svg-triangle-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.svg-triangle-container svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.text-overlay {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-overlay .v-text {
  pointer-events: auto;
  max-width: 80%;
  text-align: center;
}

/* 暗色主题 */
.dark-theme .svg-triangle-container {
  background: transparent;
}
</style>