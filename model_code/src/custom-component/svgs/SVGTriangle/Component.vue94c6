import { render, staticRenderFns } from "./Component.vue?vue&type=template&id=2d7684ce&scoped=true"
import script from "./Component.vue?vue&type=script&lang=js"
export * from "./Component.vue?vue&type=script&lang=js"
import style0 from "./Component.vue?vue&type=style&index=0&id=2d7684ce&prod&lang=scss&scoped=true"


/* normalize component */
import normalizer from "!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  "2d7684ce",
  null
  
)

export default component.exports