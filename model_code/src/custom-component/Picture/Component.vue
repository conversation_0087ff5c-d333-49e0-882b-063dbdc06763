<template>
  <div
    class="picture-component"
    :style="wrapperStyle"
    @click="handleClick"
    @mousedown="handleMouseDown"
  >
    <img
      v-if="element.propValue && element.propValue.url"
      :src="element.propValue.url"
      :alt="element.label || '图片'"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
    />
    <div v-else class="placeholder">
      <i class="el-icon-picture"></i>
      <span>{{ element.label || '图片' }}</span>
    </div>

    <!-- 设备状态指示器 -->
    <div v-if="showStatus" class="device-status" :class="statusClass">
      <i :class="statusIcon"></i>
    </div>

    <!-- 设备信息悬浮框 -->
    <div v-if="showDeviceInfo && deviceInfo" class="device-info-tooltip">
      <div class="info-item">
        <span class="label">设备编号:</span>
        <span class="value">{{ deviceInfo.sn || '未设置' }}</span>
      </div>
      <div class="info-item" v-if="deviceInfo.power !== undefined">
        <span class="label">功率:</span>
        <span class="value">{{ deviceInfo.power }}W</span>
      </div>
      <div class="info-item" v-if="deviceInfo.voltage !== undefined">
        <span class="label">电压:</span>
        <span class="value">{{ deviceInfo.voltage }}V</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VPicture',
  props: {
    element: {
      type: Object,
      required: true
    },
    id: String
  },
  data() {
    return {
      imageLoaded: false,
      imageError: false,
      showDeviceInfo: false
    }
  },
  computed: {
    wrapperStyle() {
      return {
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        borderRadius: this.element.style?.borderRadius || '0px'
      }
    },

    imageStyle() {
      const flip = this.element.propValue?.flip || {}
      return {
        width: '100%',
        height: '100%',
        objectFit: 'contain',
        transform: `scaleX(${flip.horizontal ? -1 : 1}) scaleY(${flip.vertical ? -1 : 1})`,
        transition: 'transform 0.3s ease'
      }
    },

    showStatus() {
      return this.element.device_type && this.element.device_attr
    },

    statusClass() {
      // 根据设备状态返回不同的样式类
      const status = this.deviceInfo?.status || 'unknown'
      return {
        'status-normal': status === 'normal',
        'status-warning': status === 'warning',
        'status-error': status === 'error',
        'status-offline': status === 'offline',
        'status-unknown': status === 'unknown'
      }
    },

    statusIcon() {
      const status = this.deviceInfo?.status || 'unknown'
      const iconMap = {
        normal: 'el-icon-success',
        warning: 'el-icon-warning',
        error: 'el-icon-error',
        offline: 'el-icon-remove',
        unknown: 'el-icon-question'
      }
      return iconMap[status] || 'el-icon-question'
    },

    deviceInfo() {
      return this.element.device_attr || {}
    }
  },
  methods: {
    handleClick(e) {
      e.stopPropagation()
      this.$emit('click', this.element)
    },

    handleMouseDown(e) {
      e.stopPropagation()
      this.$emit('mousedown', e, this.element)
    },

    handleImageLoad() {
      this.imageLoaded = true
      this.imageError = false
    },

    handleImageError() {
      this.imageError = true
      this.imageLoaded = false
    },

    showTooltip() {
      if (this.showStatus) {
        this.showDeviceInfo = true
      }
    },

    hideTooltip() {
      this.showDeviceInfo = false
    }
  },
  mounted() {
    // 添加鼠标悬停事件
    this.$el.addEventListener('mouseenter', this.showTooltip)
    this.$el.addEventListener('mouseleave', this.hideTooltip)
  },

  beforeDestroy() {
    // 清理事件监听器
    this.$el.removeEventListener('mouseenter', this.showTooltip)
    this.$el.removeEventListener('mouseleave', this.hideTooltip)
  }
}
</script>

<style scoped>
.picture-component {
  user-select: none;
  position: relative;
}

.placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
}

.placeholder i {
  font-size: 24px;
  margin-bottom: 8px;
}

.device-status {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  z-index: 10;
}

.status-normal {
  background-color: #67c23a;
}

.status-warning {
  background-color: #e6a23c;
}

.status-error {
  background-color: #f56c6c;
}

.status-offline {
  background-color: #909399;
}

.status-unknown {
  background-color: #c0c4cc;
}

.device-info-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-top: 4px;
}

.device-info-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-bottom-color: rgba(0, 0, 0, 0.8);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  margin-right: 8px;
  opacity: 0.8;
}

.info-item .value {
  font-weight: bold;
}

/* 暗色主题 */
.dark-theme .placeholder {
  background: #2d2d2d;
  border-color: #404040;
  color: #cccccc;
}
</style>