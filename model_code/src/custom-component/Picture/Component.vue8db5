import { render, staticRenderFns } from "./Component.vue?vue&type=template&id=16ed21ac"
import script from "./Component.vue?vue&type=script&lang=js"
export * from "./Component.vue?vue&type=script&lang=js"


/* normalize component */
import normalizer from "!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports