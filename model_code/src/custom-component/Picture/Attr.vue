<template>
  <div class="attr-list">
    <!-- 通用属性 -->
    <CommonAttr />

    <!-- 图片特有属性 -->
    <div class="attr-section">
      <h4>图片属性</h4>
      <el-form label-width="80px" size="small">
        <!-- 图片URL -->
        <el-form-item label="图片地址">
          <el-input
            v-model="curComponent.propValue.url"
            placeholder="请输入图片地址"
            @change="updateComponent"
          />
        </el-form-item>

        <!-- 镜像翻转 -->
        <el-form-item label="镜像翻转">
          <div style="clear: both;">
            <el-checkbox
              v-model="curComponent.propValue.flip.horizontal"
              @change="updateComponent"
            >
              水平翻转
            </el-checkbox>
            <el-checkbox
              v-model="curComponent.propValue.flip.vertical"
              @change="updateComponent"
            >
              垂直翻转
            </el-checkbox>
          </div>
        </el-form-item>

        <!-- 边框圆角 -->
        <el-form-item label="圆角">
          <el-input-number
            v-model="borderRadius"
            :min="0"
            :max="50"
            @change="updateBorderRadius"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 设备属性 (仅当是设备组件时显示) -->
    <div v-if="curComponent.device_type" class="attr-section">
      <h4>设备属性</h4>
      <el-form label-width="80px" size="small">
        <!-- 设备类型显示 -->
        <el-form-item label="设备类型">
          <el-tag :type="deviceTypeColor">{{ deviceTypeName }}</el-tag>
        </el-form-item>

        <!-- 设备编号 -->
        <el-form-item label="设备编号">
          <el-input
            v-model="curComponent.device_attr.sn"
            placeholder="请输入设备编号"
            @change="updateComponent"
          />
        </el-form-item>

        <!-- 设备代码 (如果存在) -->
        <el-form-item v-if="curComponent.device_attr.hasOwnProperty('code')" label="设备代码">
          <el-input
            v-model="curComponent.device_attr.code"
            placeholder="请输入设备代码"
            @change="updateComponent"
          />
        </el-form-item>

        <!-- 设备状态 -->
        <el-form-item label="设备状态">
          <el-select
            v-model="deviceStatus"
            placeholder="请选择设备状态"
            @change="updateDeviceStatus"
          >
            <el-option label="正常" value="normal"></el-option>
            <el-option label="警告" value="warning"></el-option>
            <el-option label="错误" value="error"></el-option>
            <el-option label="离线" value="offline"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CommonAttr from '../common/CommonAttr.vue'

export default {
  name: 'PictureAttr',
  components: {
    CommonAttr
  },
  computed: {
    ...mapState(['curComponent']),

    borderRadius: {
      get() {
        const radius = this.curComponent.style?.borderRadius || '0px'
        return parseInt(radius) || 0
      },
      set(value) {
        this.updateBorderRadius(value)
      }
    },

    deviceTypeName() {
      const typeMap = {
        2: '逆变器',
        3: '采集器',
        4: '光伏组件',
        5: '光伏组件'
      }
      return typeMap[this.curComponent.device_type] || '未知设备'
    },

    deviceTypeColor() {
      const colorMap = {
        2: 'primary',   // 逆变器
        3: 'success',   // 采集器
        4: 'warning',   // 光伏组件
        5: 'warning'    // 光伏组件
      }
      return colorMap[this.curComponent.device_type] || 'info'
    },

    deviceStatus: {
      get() {
        return this.curComponent.device_attr?.status || 'normal'
      },
      set(value) {
        this.updateDeviceStatus(value)
      }
    }
  },
  methods: {
    updateComponent() {
      // 触发组件更新
      this.$forceUpdate()
    },

    updateBorderRadius(value) {
      if (!this.curComponent.style) {
        this.$set(this.curComponent, 'style', {})
      }
      this.$set(this.curComponent.style, 'borderRadius', value + 'px')
      this.updateComponent()
    },

    updateDeviceStatus(status) {
      if (!this.curComponent.device_attr) {
        this.$set(this.curComponent, 'device_attr', {})
      }
      this.$set(this.curComponent.device_attr, 'status', status)
      this.updateComponent()
    }
  }
}
</script>

<style scoped>
.attr-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.attr-section:last-child {
  border-bottom: none;
}

.attr-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 暗色主题 */
.dark-theme .attr-section {
  border-bottom-color: #404040;
}

.dark-theme .attr-section h4 {
  color: #cccccc;
}
</style>