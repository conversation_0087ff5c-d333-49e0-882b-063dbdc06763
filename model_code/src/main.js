import Vue from 'vue'
import ElementUI from 'element-ui'
import App from './App'
import store from './store'
import router from './router'
import '@/custom-component' // 注册自定义组件

import '@/assets/iconfont/iconfont.css'
import '@/styles/animate.scss'
import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/reset.css'
import '@/styles/global.scss'
import '@/styles/dark.scss'
import Vue2OrgTree from 'vue-tree-color'

Vue.use(ElementUI, {
    size: 'small'
})
Vue.config.productionTip = false
Vue.use(Vue2OrgTree)
new Vue({
    el: '#app',
    router,
    store,
    render: (h) => h(App),
})
