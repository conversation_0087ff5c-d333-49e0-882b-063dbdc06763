<template>
  <div class="bg">
    <!-- 调试按钮 -->
    <div @click="login">111</div>
    <div @click="login3">获取数据</div>

    <!-- 顶部统计区域 -->
    <div class="maintop">
      <div class="main-top">
        <div>
          <span>组件百分比</span>
          <img class="triangle" src="@/assets/triangle.png" alt="triangle">
        </div>
        <div>
          <span>日发电量(度)</span>
          <img class="triangle" src="@/assets/triangle.png" alt="triangle">
        </div>
        <div>
          <span>12.837</span>
          <span class="triangle_num">KWh</span>
        </div>
      </div>

      <!-- 设备状态统计 -->
      <div class="main-tag">
        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/total.png" alt="总计">
            <span>总计</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/normal.png" alt="正常">
            <span>正常</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/fault.png" alt="故障">
            <span>故障</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/abnormal.png" alt="异常">
            <span>异常</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/offline.png" alt="离线">
            <span>离线</span>
          </div>
          <span class="total_num">126</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-all">
      <div class="main">
        <!-- 采集器列表 -->
        <div
          v-for="(item, index) in ComponentListTo.collectorList"
          :key="index"
          class="cjq"
        >
          <div class="inverter_info">
            <img class="inverter_img" src="@/assets/bzq.png" alt="采集器">
            <span class="inverter_text">{{ item.cloudName }}</span>
          </div>
        </div>

        <!-- 组串列表 -->
        <div class="groupList">
          <div
            v-for="(item, index) in ComponentListTo.groupList"
            :key="index"
            class="inverter_components"
          >
            <div>
              <img
                class="inverter"
                :src="item.groupName === '常规组串'
                  ? require('@/assets/components.png')
                  : require('@/assets/Intelligent.png')"
                alt="组串"
              >

              <!-- 组件数据 -->
              <div class="groupList_data">
                <div
                  v-for="(dataItem, dataIndex) in ComponentListTo.data"
                  :key="dataIndex"
                  class="classify-cell-recom"
                >
                  <img
                    class="inverters"
                    src="@/assets/component_lx.png"
                    alt="组件"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Previews',
  data() {
    return {
      ComponentListTo: {
        collectorList: [],
        groupList: [],
        data: []
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    login() {
      console.log('登录测试')
    },

    login3() {
      console.log('获取数据')
      this.fetchData()
    },

    initData() {
      this.ComponentListTo = {
        collectorList: [
          { cloudName: '采集器1' },
          { cloudName: '采集器2' }
        ],
        groupList: [
          { groupName: '常规组串' },
          { groupName: '智能组串' }
        ],
        data: new Array(20).fill({}).map((_, index) => ({
          id: index + 1,
          name: `组件${index + 1}`
        }))
      }
    },

    fetchData() {
      console.log('正在获取数据...')
    }
  }
}
</script>

<style scoped>
.bg {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.maintop {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.main-top div {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  flex: 1;
  margin: 0 5px;
}

.triangle {
  width: 20px;
  height: 20px;
}

.triangle_num {
  font-size: 12px;
  color: #666;
}

.main-tag {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.triangle-t {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  text-align: center;
  flex: 1;
  min-width: 120px;
}

.triangle—mian {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.triangle-icon {
  width: 20px;
  height: 20px;
}

.total_num {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.main-all {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cjq {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
}

.inverter_info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.inverter_img {
  width: 40px;
  height: 40px;
}

.inverter_text {
  font-size: 16px;
  font-weight: 500;
}

.groupList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.inverter_components {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
}

.inverter {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
}

.groupList_data {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 10px;
}

.classify-cell-recom {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.inverters {
  width: 30px;
  height: 30px;
}
</style>