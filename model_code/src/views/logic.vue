<template>
  <div :class="['home', { dark: isDarkMode }]">
    <!-- 顶部标题栏 -->
    <div class="main">
      <img class="triangle" src="@/assets/power.png" alt="logo">
      <span class="main_title">{{ name }}</span>
    </div>

    <!-- 主要内容区域 -->
    <main>
      <!-- 左侧面板 -->
      <section :class="['left', { active: leftList, inactive: !leftList }]">
        <ComponentList />
        <RealTimeComponentList />
      </section>

      <!-- 左侧面板切换按钮 -->
      <el-button
        class="btn show-list left-btn"
        title="show-list-btn"
        :icon="leftList ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"
        @click="isShowLeft"
      />

      <!-- 工具栏 -->
      <Toolbar />

      <!-- 中央编辑区域 -->
      <section
        class="center"
        :style="{ marginRight: rightList ? '288px' : '10px' }"
      >
        <div
          class="content"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @mousedown="handleMouseDown"
          @mouseup="deselectCurComponent"
        >
          <Editor />
        </div>
      </section>

      <!-- 右侧属性面板 -->
      <section :class="['right', { active: rightList, inactive: !rightList }]">
        <!-- 组件属性面板 -->
        <el-tabs v-if="curComponent" v-model="activeName">
          <el-tab-pane label="属性" name="attr">
            <component :is="curComponent.component + 'Attr'" />
          </el-tab-pane>
          <el-tab-pane label="动画" name="animation" style="padding-top: 20px;">
            <AnimationList />
          </el-tab-pane>
          <el-tab-pane label="事件" name="events" style="padding-top: 20px;">
            <EventList />
          </el-tab-pane>
        </el-tabs>

        <!-- 画布属性面板 -->
        <CanvasAttr v-else />
      </section>

      <!-- 右侧面板切换按钮 -->
      <el-button
        class="btn show-list right-btn"
        title="show-list-btn"
        :icon="rightList ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"
        @click="isShowRight"
      />
    </main>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ComponentList from '@/components/ComponentList.vue'
import RealTimeComponentList from '@/components/RealTimeComponentList.vue'
import Toolbar from '@/components/Toolbar.vue'
import Editor from '@/components/Editor.vue'
import AnimationList from '@/components/AnimationList.vue'
import EventList from '@/components/EventList.vue'
import CanvasAttr from '@/components/CanvasAttr.vue'

export default {
  name: 'Logic',
  components: {
    ComponentList,
    RealTimeComponentList,
    Toolbar,
    Editor,
    AnimationList,
    EventList,
    CanvasAttr
  },
  data() {
    return {
      leftList: true,
      rightList: true,
      activeName: 'attr',
      name: '光伏监控系统'
    }
  },
  computed: {
    ...mapState(['isDarkMode', 'curComponent'])
  },
  methods: {
    isShowLeft() {
      this.leftList = !this.leftList
    },

    isShowRight() {
      this.rightList = !this.rightList
    },

    handleDrop(e) {
      e.preventDefault()
      // 处理拖拽放置
      console.log('Drop event:', e)
    },

    handleDragOver(e) {
      e.preventDefault()
    },

    handleMouseDown(e) {
      // 处理鼠标按下事件
      console.log('Mouse down:', e)
    },

    deselectCurComponent() {
      // 取消选中当前组件
      this.$store.commit('setCurComponent', { component: null, index: null })
    }
  }
}
</script>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.home.dark {
  background: #1a1a1a;
}

.main {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.triangle {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.main_title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

main {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.left {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  z-index: 10;
  overflow-y: auto;
}

.left.inactive {
  width: 0;
  overflow: hidden;
}

.right {
  width: 280px;
  background: white;
  border-left: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  z-index: 10;
  overflow-y: auto;
}

.right.inactive {
  width: 0;
  overflow: hidden;
}

.center {
  flex: 1;
  transition: all 0.3s ease;
  position: relative;
}

.content {
  width: 100%;
  height: 100%;
  position: relative;
}

.btn.show-list {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  border-radius: 0;
  padding: 8px 4px;
}

.left-btn {
  left: 280px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.left.inactive + .left-btn {
  left: 0;
}

.right-btn {
  right: 280px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.right.inactive ~ .right-btn {
  right: 0;
}

/* 暗色主题 */
.home.dark .main {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.home.dark .main_title {
  color: #ffffff;
}

.home.dark .left,
.home.dark .right {
  background: #2d2d2d;
  border-color: #404040;
}
</style>