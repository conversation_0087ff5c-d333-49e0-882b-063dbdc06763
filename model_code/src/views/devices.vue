<template>
  <div class="device">
    <!-- 顶部统计区域 -->
    <div class="main">
      <div class="main-top">
        <div>
          <span>组件百分比</span>
          <img class="triangle" src="@/assets/triangle.png" alt="triangle">
        </div>
        <div>
          <span>日发电量(度)</span>
          <img class="triangle" src="@/assets/triangle.png" alt="triangle">
        </div>
        <div>
          <span>12.837</span>
          <span class="triangle_num">KWh</span>
        </div>
      </div>

      <!-- 设备状态统计 -->
      <div class="main-tag">
        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/total.png" alt="总计">
            <span>总计</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/normal.png" alt="正常">
            <span>正常</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/fault.png" alt="故障">
            <span>故障</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/abnormal.png" alt="异常">
            <span>异常</span>
          </div>
          <span class="total_num">126</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/offline.png" alt="离线">
            <span>离线</span>
          </div>
          <span class="total_num">126</span>
        </div>
      </div>
    </div>

    <!-- 设备树形图容器 -->
    <div class="canvas-container">
      <vue2-org-tree
        :data="data"
        :horizontal="true"
        name="test"
        :node-class="NodeClass"
        :label-class-name="labelClassName"
        :collapsable="true"
        :render-content="renderContent"
        @on-expand="onExpand"
        @on-node-mouseover="onMouseover"
        @on-node-mouseout="onMouseout"
      />

      <!-- 悬浮信息框 -->
      <div v-show="BasicSwich" class="floating">
        <p>chipId: {{ BasicInfo.chipId }}</p>
        <p>status: {{ BasicInfo.status }}</p>
      </div>
    </div>

    <!-- 底部缩略图 -->
    <div class="bottom">
      <div class="scaled-content">
        <vue2-org-tree
          class="org-tree-container2"
          :data="data"
          :horizontal="true"
          name="test"
          :node-class="NodeClass"
          :label-class-name="labelClassName"
          :collapsable="true"
          :render-content="renderContent"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Devices',
  data() {
    return {
      BasicSwich: false,
      data: {},
      BasicInfo: {
        chipId: '',
        status: ''
      }
    }
  },
  computed: {
    NodeClass() {
      return 'device-node'
    },

    labelClassName() {
      return 'device-label'
    }
  },
  methods: {
    renderContent(h, data) {
      return h('div', {
        class: 'device-node-content'
      }, data.label)
    },

    onExpand(e, data) {
      console.log('展开设备节点:', data)
    },

    onMouseover(e, data) {
      this.BasicInfo = {
        chipId: data.chipId || '',
        status: data.status || ''
      }
      this.BasicSwich = true
    },

    onMouseout() {
      this.BasicSwich = false
    }
  }
}
</script>

<style scoped>
.device {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.main {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.main-top div {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  flex: 1;
  margin: 0 5px;
}

.triangle {
  width: 20px;
  height: 20px;
}

.triangle_num {
  font-size: 12px;
  color: #666;
}

.main-tag {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.triangle-t {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  text-align: center;
  flex: 1;
  min-width: 120px;
}

.triangle—mian {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.triangle-icon {
  width: 20px;
  height: 20px;
}

.total_num {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.canvas-container {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.floating {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 4px;
  z-index: 1000;
}

.floating p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.floating p:last-child {
  margin-bottom: 0;
}

.bottom {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 200px;
  overflow: hidden;
}

.scaled-content {
  transform: scale(0.5);
  transform-origin: top left;
  width: 200%;
  height: 200%;
}

.org-tree-container2 {
  width: 100%;
  height: 100%;
}
</style>