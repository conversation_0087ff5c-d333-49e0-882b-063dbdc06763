import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const routes = [{
        path: '/',
        name: 'Home',
        component: () =>
            import ('@/views/Home'),
    },
    {
        path: '/preview',
        name: 'preview',
        component: () =>
            import ('@/views/Preview'),
    },

    {
        path: '/previews',
        name: 'previews',
        component: () =>
            import ('@/views/Previews'),
    },
    {
        path: '/devices',
        name: 'devices',
        component: () =>
            import ('@/views/devices'),
    },
    {
        path: '/logic',
        name: 'logic',
        component: () =>
            import ('@/views/logic'),
    },


]

export default new Router({
    // mode: 'history',
    base: process.env.BASE_URL,
    routes,
})