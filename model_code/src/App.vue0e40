import { render, staticRenderFns } from "./App.vue?vue&type=template&id=5a20a338"
var script = {}
import style0 from "./App.vue?vue&type=style&index=0&id=5a20a338&prod&lang=css"


/* normalize component */
import normalizer from "!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js"
var component = normalizer(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

export default component.exports