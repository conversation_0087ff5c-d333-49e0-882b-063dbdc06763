const path = require('path')

module.exports = {
  lintOnSave: false,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      }
    }
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        }
      }
    }
  },
  devServer: {
    proxy: {
      '/photovoltaic-manager': {
        target: 'http://localhost:8090/photovoltaic-manager',
        changeOrigin: true,
        pathRewrite: {
          '^/photovoltaic-manager': ''
        }
      }
    }
  }
}
